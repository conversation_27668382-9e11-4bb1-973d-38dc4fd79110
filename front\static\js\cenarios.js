/**
 * Cenarios.js - Auditoria Fiscal
 * Funções para gerenciar as páginas de cenários
 */

// Variáveis globais - usando namespace para evitar conflitos
window.cenarios = window.cenarios || {
  selectedCompany: null,
  selectedYear: null,
  selectedMonth: null,
};

document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - cenarios.js');

  // Configurar os links dos cards
  setupCardLinks();

  // Configurar filtros
  setupFilters();

  // Carregar contadores para os cards
  loadCardCounters();
});

/**
 * Configura os links dos cards
 */
function setupCardLinks() {
  const cardLinks = document.querySelectorAll('.card-link-wrapper');

  cardLinks.forEach((link) => {
    // Não precisamos adicionar event listeners, pois os links já funcionam nativamente
    // Apenas para logging
    const tipo = link.dataset.tipo;
    const isEntrada = link.closest('#page-cenarios-entrada') !== null;
    console.log(
      `Card link configurado: ${tipo} (${isEntrada ? 'Entrada' : 'Saída'})`,
    );
  });
}

/**
 * Configura os filtros da página
 */
function setupFilters() {
  // Obter os filtros do header e da variável global
  // Usar a variável global selectedCompany definida em common.js ou dashboard.js
  window.cenarios.selectedCompany =
    window.selectedCompany || localStorage.getItem('selectedCompany');

  // Obter os valores dos seletores de ano e mês
  window.cenarios.selectedYear = document.getElementById('year-select')?.value;
  window.cenarios.selectedMonth =
    document.getElementById('month-select')?.value;

  console.log('Filtros iniciais:', {
    company: window.cenarios.selectedCompany,
    year: window.cenarios.selectedYear,
    month: window.cenarios.selectedMonth,
  });

  // Adicionar event listeners para os filtros
  const companySelect = document.getElementById('company-select');
  if (companySelect) {
    companySelect.addEventListener('change', function () {
      window.cenarios.selectedCompany = this.value;
      console.log('Empresa selecionada:', window.cenarios.selectedCompany);
      loadCardCounters();
    });
  }

  const yearSelect = document.getElementById('year-select');
  if (yearSelect) {
    yearSelect.addEventListener('change', function () {
      window.cenarios.selectedYear = this.value;
      console.log('Ano selecionado:', window.cenarios.selectedYear);
      loadCardCounters();
    });
  }

  const monthSelect = document.getElementById('month-select');
  if (monthSelect) {
    monthSelect.addEventListener('change', function () {
      window.cenarios.selectedMonth = this.value;
      console.log('Mês selecionado:', window.cenarios.selectedMonth);
      loadCardCounters();
    });
  }
}

/**
 * Carrega os contadores para os cards
 */
function loadCardCounters() {
  // Verificar se há uma empresa selecionada
  if (!window.cenarios.selectedCompany) {
    console.log('Nenhuma empresa selecionada para carregar contadores');
    return;
  }

  // Obter todos os cards
  const cards = document.querySelectorAll('.card-link-wrapper');

  // Para cada card, carregar o contador
  cards.forEach((card) => {
    const tipo = card.dataset.tipo;
    const direcao =
      card.closest('#page-cenarios-entrada') !== null ? 'entrada' : 'saida';

    // Obter o elemento do contador
    const counterElement = card.querySelector('.counter');
    if (!counterElement) return;

    // Mostrar indicador de carregamento
    counterElement.innerHTML =
      '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Carregando...</span></div>';

    // Parâmetros para a requisição
    const params = new URLSearchParams();
    params.append('empresa_id', window.cenarios.selectedCompany);
    params.append('direcao', direcao);

    // Adicionar filtros de ano e mês se estiverem definidos
    if (window.cenarios.selectedYear) {
      params.append('year', window.cenarios.selectedYear);
    }

    if (window.cenarios.selectedMonth) {
      params.append('month', window.cenarios.selectedMonth);
    }

    // Fazer requisição para a API
    fetch(`/api/cenarios/${tipo}/count?${params.toString()}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // Atualizar o contador
          counterElement.innerHTML = `
            <div class="counter-item">
              <span class="counter-label">Novos:</span>
              <span class="counter-value">${data.counts.novo || 0}</span>
            </div>
            <div class="counter-item">
              <span class="counter-label">Produção:</span>
              <span class="counter-value">${data.counts.producao || 0}</span>
            </div>
            <div class="counter-item">
              <span class="counter-label">Inconsistentes:</span>
              <span class="counter-value">${
                data.counts.inconsistente || 0
              }</span>
            </div>
            <div class="counter-item">
              <span class="counter-label">Conformes:</span>
              <span class="counter-value">${data.counts.conforme || 0}</span>
            </div>
          `;
        } else {
          // Mostrar erro
          counterElement.innerHTML =
            '<span class="text-danger">Erro ao carregar contadores</span>';
        }
      })
      .catch((error) => {
        console.error(
          `Erro ao carregar contadores para ${tipo} (${direcao}):`,
          error,
        );
        counterElement.innerHTML =
          '<span class="text-danger">Erro ao carregar contadores</span>';
      });
  });
}
