/*
 * Auditoria Dashboard CSS
 * Interface limpa com expansão de cenários
 */

/* Botão de expansão para mostrar/ocultar cenário */
.expand-scenario-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.1rem;
  padding: 0.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0.25rem;
}

.expand-scenario-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #495057;
}

.expand-scenario-btn.expanded {
  color: #007bff;
  transform: rotate(90deg);
}

/* Dark mode para botão de expansão */
body.dark-theme .expand-scenario-btn {
  color: #adb5bd;
}

body.dark-theme .expand-scenario-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e9ecef;
}

body.dark-theme .expand-scenario-btn.expanded {
  color: #66b3ff;
}

/* <PERSON><PERSON> cenário (inicialmente ocultas) */
.scenario-row {
  display: none;
  background-color: rgba(40, 167, 69, 0.1) !important;
  border-left: 4px solid #28a745;
}

.scenario-row.show {
  display: table-row;
  animation: slideDown 0.3s ease-out;
}

/* Dark mode para linhas de cenário */
body.dark-theme .scenario-row {
  background-color: rgba(105, 219, 124, 0.15) !important;
  border-left-color: #69db7c;
}

/* Animação de expansão */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Indicador visual para linha de nota inconsistente */
.nota-inconsistente-row {
  border-left: 4px solid #dc3545;
  background-color: rgba(220, 53, 69, 0.05) !important;
}

/* Dark mode para linha de nota inconsistente */
body.dark-theme .nota-inconsistente-row {
  border-left-color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1) !important;
}

/* Cards */
.dashboard-card {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  border: none;
  min-height: 180px;
  overflow: hidden;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-body {
  padding: 1.25rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dashboard-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--gray-700);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dashboard-card .card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
}

.dashboard-card .card-value {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  line-height: 1.2;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
  overflow-wrap: break-word;
}

.dashboard-card .card-value.large-value {
  font-size: 1.3rem;
}

.dashboard-card .card-value.extra-large-value {
  font-size: 1.1rem;
}

.dashboard-card .card-subtitle {
  font-size: 0.85rem;
  color: var(--gray-500);
  line-height: 1.3;
  margin-bottom: 0.25rem;
  text-align: center;
}

.dashboard-card .card-subtitle:last-child {
  margin-bottom: 0;
}

/* Cards maiores (col-md-6) - Valores a Maior e a Menor */
.col-md-6 .dashboard-card {
  min-height: 200px;
}

.col-md-6 .dashboard-card .card-body {
  padding: 1.5rem;
}

.col-md-6 .dashboard-card .card-title {
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.col-md-6 .dashboard-card .card-value {
  font-size: 2rem;
}

.col-md-6 .dashboard-card .card-value.large-value {
  font-size: 1.7rem;
}

.col-md-6 .dashboard-card .card-value.extra-large-value {
  font-size: 1.4rem;
}

.col-md-6 .dashboard-card .card-subtitle {
  font-size: 0.95rem;
}

/* Cores específicas para valores */
.dashboard-card .card-value.text-danger {
  color: #dc3545 !important;
}

.dashboard-card .card-value.text-success {
  color: #28a745 !important;
}

/* Tabela de resultados */

.table {
  margin-bottom: 0;
}

.table thead th {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  border: none;
  padding: 1rem;
}

.table tbody td {
  padding: 0.75rem 1rem;
  vertical-align: middle;
}

.nota-row {
  background-color: rgba(255, 255, 255, 0.9);
}

.cenario-row {
  background-color: rgba(240, 249, 255, 0.9);
}

.text-danger {
  color: #dc3545 !important;
  font-weight: 600;
}

.text-success {
  color: #28a745 !important;
  font-weight: 600;
}

/* Responsividade */
@media (max-width: 1200px) {
  .dashboard-card .card-value {
    font-size: 1.4rem;
  }

  .dashboard-card .card-value.large-value {
    font-size: 1.2rem;
  }

  .dashboard-card .card-value.extra-large-value {
    font-size: 1rem;
  }

  /* Cards maiores em telas médias */
  .col-md-6 .dashboard-card .card-value {
    font-size: 1.8rem;
  }

  .col-md-6 .dashboard-card .card-value.large-value {
    font-size: 1.5rem;
  }

  .col-md-6 .dashboard-card .card-value.extra-large-value {
    font-size: 1.2rem;
  }
}

@media (max-width: 992px) {
  .dashboard-card {
    min-height: 160px;
  }

  .dashboard-card .card-body {
    padding: 1rem;
  }

  .dashboard-card .card-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .dashboard-card .card-value {
    font-size: 1.3rem;
  }

  .dashboard-card .card-value.large-value {
    font-size: 1.1rem;
  }

  .dashboard-card .card-value.extra-large-value {
    font-size: 0.95rem;
  }

  .dashboard-card .card-subtitle {
    font-size: 0.8rem;
  }

  /* Cards maiores em tablets */
  .col-md-6 .dashboard-card {
    min-height: 180px;
  }

  .col-md-6 .dashboard-card .card-body {
    padding: 1.25rem;
  }

  .col-md-6 .dashboard-card .card-title {
    font-size: 1.2rem;
  }

  .col-md-6 .dashboard-card .card-value {
    font-size: 1.6rem;
  }

  .col-md-6 .dashboard-card .card-value.large-value {
    font-size: 1.4rem;
  }

  .col-md-6 .dashboard-card .card-value.extra-large-value {
    font-size: 1.1rem;
  }

  .col-md-6 .dashboard-card .card-subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .col-md-3,
  .col-md-6 {
    margin-bottom: 1rem;
  }

  .dashboard-card {
    min-height: 140px;
  }

  .dashboard-card .card-body {
    padding: 0.875rem;
  }

  .dashboard-card .card-title {
    font-size: 0.95rem;
  }

  .dashboard-card .card-value {
    font-size: 1.2rem;
  }

  .dashboard-card .card-value.large-value {
    font-size: 1rem;
  }

  .dashboard-card .card-value.extra-large-value {
    font-size: 0.9rem;
  }

  .dashboard-card .card-subtitle {
    font-size: 0.75rem;
  }

  /* Cards maiores em mobile */
  .col-md-6 .dashboard-card {
    min-height: 160px;
  }

  .col-md-6 .dashboard-card .card-body {
    padding: 1rem;
  }

  .col-md-6 .dashboard-card .card-title {
    font-size: 1.1rem;
  }

  .col-md-6 .dashboard-card .card-value {
    font-size: 1.4rem;
  }

  .col-md-6 .dashboard-card .card-value.large-value {
    font-size: 1.2rem;
  }

  .col-md-6 .dashboard-card .card-value.extra-large-value {
    font-size: 1rem;
  }

  .col-md-6 .dashboard-card .card-subtitle {
    font-size: 0.85rem;
  }
}

@media (max-width: 576px) {
  .dashboard-card {
    min-height: 120px;
  }

  .dashboard-card .card-body {
    padding: 0.75rem;
  }

  .dashboard-card .card-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .dashboard-card .card-value {
    font-size: 1.1rem;
  }

  .dashboard-card .card-value.large-value {
    font-size: 0.95rem;
  }

  .dashboard-card .card-value.extra-large-value {
    font-size: 0.85rem;
  }

  .dashboard-card .card-subtitle {
    font-size: 0.7rem;
    line-height: 1.2;
  }

  /* Cards maiores em telas muito pequenas */
  .col-md-6 .dashboard-card {
    min-height: 140px;
  }

  .col-md-6 .dashboard-card .card-body {
    padding: 0.875rem;
  }

  .col-md-6 .dashboard-card .card-title {
    font-size: 1rem;
  }

  .col-md-6 .dashboard-card .card-value {
    font-size: 1.3rem;
  }

  .col-md-6 .dashboard-card .card-value.large-value {
    font-size: 1.1rem;
  }

  .col-md-6 .dashboard-card .card-value.extra-large-value {
    font-size: 0.95rem;
  }

  .col-md-6 .dashboard-card .card-subtitle {
    font-size: 0.8rem;
  }
}

/* Tabs do Dashboard */
.dashboard-tabs-container {
  margin-top: 1rem;
}

.dashboard-tabs-container .nav-tabs {
  border-bottom: 2px solid #dee2e6;
}

.dashboard-tabs-container .nav-tabs .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.dashboard-tabs-container .nav-tabs .nav-link:hover {
  border-color: transparent;
  color: var(--primary);
  background-color: rgba(var(--primary-rgb), 0.1);
}

.dashboard-tabs-container .nav-tabs .nav-link.active {
  color: var(--primary);
  background-color: transparent;
  border-color: transparent transparent var(--primary);
  font-weight: 600;
}

.dashboard-tabs-container .tab-content {
  padding-top: 1.5rem;
}

/* Filtros da tab de detalhamento - Classes específicas */
.audit-dashboard-filters {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.audit-dashboard-filters .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.audit-dashboard-filters .form-control,
.audit-dashboard-filters .form-select {
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.audit-dashboard-filters .form-control:focus,
.audit-dashboard-filters .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* Tabela de detalhamento - Estilos simplificados */
#audit-dashboard-detail-table {
  font-size: 0.9rem;
}

#audit-dashboard-detail-table th {
  text-align: center;
  vertical-align: middle;
  font-weight: 600;
}

#audit-dashboard-detail-table td {
  vertical-align: middle;
}

/* Botões de ação */
.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.action-buttons .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
}

.btn-view {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: white;
}

.btn-view:hover {
  background-color: #138496;
  border-color: #117a8b;
  color: white;
}

.btn-report {
  background-color: #fd7e14;
  border-color: #fd7e14;
  color: white;
}

.btn-report:hover {
  background-color: #e8650e;
  border-color: #dc5f0a;
  color: white;
}

/* Dark mode */
.dark-mode .dashboard-card {
  background-color: var(--gray-800);
  border: 1px solid var(--gray-700);
}

.dark-mode .dashboard-card .card-title {
  color: var(--gray-300);
}

.dark-mode .dashboard-card .card-value {
  color: var(--primary-light);
}

.dark-mode .dashboard-card .card-subtitle {
  color: var(--gray-400);
}

.dark-mode .table thead th {
  background-color: var(--gray-700);
}

.dark-mode .nota-row {
  background-color: var(--gray-800);
}

.dark-mode .cenario-row {
  background-color: var(--gray-700);
}

.dark-mode .dashboard-tabs-container .nav-tabs {
  border-bottom-color: var(--gray-600);
}

.dark-mode .dashboard-tabs-container .nav-tabs .nav-link {
  color: var(--gray-400);
}

.dark-mode .dashboard-tabs-container .nav-tabs .nav-link:hover {
  color: var(--primary-light);
  background-color: rgba(var(--primary-rgb), 0.1);
}

.dark-mode .dashboard-tabs-container .nav-tabs .nav-link.active {
  color: var(--primary-light);
  border-bottom-color: var(--primary-light);
}

.dark-mode .audit-dashboard-filters {
  background-color: var(--gray-800) !important;
  border: 1px solid var(--gray-700) !important;
}

.dark-mode .audit-dashboard-filters .row {
  background-color: transparent !important;
}

.dark-mode .audit-dashboard-filters .col-md-2 {
  background-color: transparent !important;
}

.dark-mode .audit-dashboard-filters .form-label {
  color: var(--gray-300) !important;
}

.dark-mode .audit-dashboard-filters .form-control,
.dark-mode .audit-dashboard-filters .form-select {
  background-color: var(--gray-700) !important;
  border-color: var(--gray-600) !important;
  color: var(--gray-200) !important;
}

.dark-mode .audit-dashboard-filters .form-control:focus,
.dark-mode .audit-dashboard-filters .form-select:focus {
  background-color: var(--gray-700) !important;
  border-color: var(--primary-light) !important;
  color: var(--gray-200) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25) !important;
}

.dark-mode .audit-dashboard-filters .form-control::placeholder {
  color: var(--gray-400) !important;
}

/* Estilos específicos para valores divergentes */
.text-danger {
  font-weight: 600;
}

.text-success {
  font-weight: 600;
}

.dark-mode .btn-view {
  background-color: #20c997;
  border-color: #20c997;
}

.dark-mode .btn-view:hover {
  background-color: #1aa085;
  border-color: #198c73;
}

.dark-mode .btn-report {
  background-color: #fd7e14;
  border-color: #fd7e14;
}

.dark-mode .btn-report:hover {
  background-color: #e8650e;
  border-color: #dc5f0a;
}

.dark-mode .text-danger {
  color: #ff6b6b !important;
}

.dark-mode .text-success {
  color: #69db7c !important;
}

/* Estilos para botões de análise do analista */
.action-buttons .btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.action-buttons .btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
  color: #212529;
}

.action-buttons .btn-success.btn-sm {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.action-buttons .btn-success.btn-sm:hover {
  background-color: #218838;
  border-color: #1e7e34;
  color: white;
}

/* Dark mode para botões de análise */
.dark-mode .action-buttons .btn-warning {
  background-color: #ffd93d;
  border-color: #ffd93d;
  color: #2d3748;
}

.dark-mode .action-buttons .btn-warning:hover {
  background-color: #ffcc02;
  border-color: #ffcc02;
  color: #2d3748;
}

.dark-mode .action-buttons .btn-success.btn-sm {
  background-color: #69db7c;
  border-color: #69db7c;
  color: #2d3748;
}

.dark-mode .action-buttons .btn-success.btn-sm:hover {
  background-color: #51cf66;
  border-color: #51cf66;
  color: #2d3748;
}

/* Indicadores visuais para inconsistências analisadas */
.nota-inconsistente-row.analisada {
  border-left-color: #28a745;
  background-color: rgba(40, 167, 69, 0.05) !important;
}

.dark-mode .nota-inconsistente-row.analisada {
  border-left-color: #69db7c;
  background-color: rgba(105, 219, 124, 0.1) !important;
}

/* Estilos para o resumo de inconsistências */
.alert-warning .row .d-flex {
  margin-bottom: 0.5rem;
}

.alert-warning .row .d-flex i {
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

/* Dark mode para alertas */
.dark-mode .alert-warning {
  background-color: rgba(255, 217, 61, 0.1);
  border-color: #ffd93d;
  color: #e2e8f0;
}
