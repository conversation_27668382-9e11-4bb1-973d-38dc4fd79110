from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class Escritorio(db.Model):
    __tablename__ = 'escritorio'
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(255), nullable=False)
    cnpj = db.Column(db.String(18), nullable=False, unique=True)
    endereco = db.Column(db.Text)
    empresas = db.relationship('Empresa', backref='escritorio', lazy=True)
    usuarios = db.relationship('Usuario', backref='escritorio', lazy=True)

    def __repr__(self):
        return f"<Escritorio {self.nome}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'nome': self.nome,
            'cnpj': self.cnpj,
            'endereco': self.endereco,
        }
