#!/usr/bin/env python3
"""
Script para testar a correção do CFOP na auditoria fiscal
"""

import sys
import os
sys.path.append('./back')

from models import db, Tributo, CenarioICMS, NotaFiscalItem, AuditoriaResultado
from app import create_app
from services.auditoria_service import AuditoriaService
from sqlalchemy.orm import joinedload

def verificar_tributos_com_cfop():
    """Verifica tributos que têm CFOP na nota fiscal"""
    app = create_app()
    with app.app_context():
        print("=== VERIFICAÇÃO DE TRIBUTOS COM CFOP ===\n")
        
        # Buscar tributos com nota_fiscal_item
        tributos = Tributo.query.options(joinedload(Tributo.nota_fiscal_item)).filter(
            Tributo.nota_fiscal_item_id.isnot(None)
        ).limit(10).all()
        
        if not tributos:
            print("❌ Nenhum tributo com nota_fiscal_item encontrado!")
            return
        
        for tributo in tributos:
            print(f"🧾 Tributo ID: {tributo.id}")
            print(f"   Empresa: {tributo.empresa_id}")
            print(f"   Cliente: {tributo.cliente_id}")
            print(f"   Produto: {tributo.produto_id}")
            print(f"   Nota Fiscal Item ID: {tributo.nota_fiscal_item_id}")
            
            if tributo.nota_fiscal_item:
                print(f"   CFOP da Nota: {tributo.nota_fiscal_item.cfop}")
                print(f"   NCM da Nota: {tributo.nota_fiscal_item.ncm}")
            else:
                print("   ❌ Relacionamento nota_fiscal_item não carregado!")
            
            print("-" * 50)

def verificar_cenarios_com_cfop():
    """Verifica cenários que têm CFOP"""
    app = create_app()
    with app.app_context():
        print("\n=== VERIFICAÇÃO DE CENÁRIOS COM CFOP ===\n")
        
        # Buscar cenários em produção com CFOP
        cenarios = CenarioICMS.query.filter(
            CenarioICMS.status == 'producao',
            CenarioICMS.cfop.isnot(None)
        ).limit(10).all()
        
        if not cenarios:
            print("❌ Nenhum cenário em produção com CFOP encontrado!")
            return
        
        for cenario in cenarios:
            print(f"📋 Cenário ID: {cenario.id}")
            print(f"   Empresa: {cenario.empresa_id}")
            print(f"   Cliente: {cenario.cliente_id}")
            print(f"   Produto: {cenario.produto_id}")
            print(f"   CFOP: {cenario.cfop}")
            print(f"   NCM: {cenario.ncm}")
            print(f"   Status: {cenario.status}")
            print(f"   Ativo: {cenario.ativo}")
            print("-" * 50)

def testar_busca_cenario_com_cfop():
    """Testa a busca de cenário considerando CFOP"""
    app = create_app()
    with app.app_context():
        print("\n=== TESTE DE BUSCA DE CENÁRIO COM CFOP ===\n")
        
        # Buscar um tributo com nota fiscal
        tributo = Tributo.query.options(joinedload(Tributo.nota_fiscal_item)).filter(
            Tributo.nota_fiscal_item_id.isnot(None)
        ).first()
        
        if not tributo or not tributo.nota_fiscal_item:
            print("❌ Nenhum tributo com nota fiscal encontrado para teste!")
            return
        
        print(f"🧾 Testando com Tributo ID: {tributo.id}")
        print(f"   CFOP da Nota: {tributo.nota_fiscal_item.cfop}")
        print(f"   Cliente: {tributo.cliente_id}")
        print(f"   Produto: {tributo.produto_id}")
        
        # Criar instância do serviço de auditoria
        auditoria_service = AuditoriaService(tributo.empresa_id, None)
        
        # Testar busca de cenário
        cenario = auditoria_service._buscar_cenario_vigente('icms', tributo)
        
        if cenario:
            print(f"✅ Cenário encontrado: ID={cenario.id}")
            print(f"   CFOP do Cenário: {cenario.cfop}")
            print(f"   Status: {cenario.status}")
            print(f"   Ativo: {cenario.ativo}")
            
            # Verificar se o CFOP corresponde
            if cenario.cfop == tributo.nota_fiscal_item.cfop:
                print("✅ CFOP corresponde!")
            else:
                print(f"❌ CFOP não corresponde! Cenário: {cenario.cfop}, Nota: {tributo.nota_fiscal_item.cfop}")
        else:
            print("❌ Nenhum cenário encontrado!")
            
            # Buscar cenários disponíveis para diagnóstico
            cenarios_disponiveis = CenarioICMS.query.filter(
                CenarioICMS.empresa_id == tributo.empresa_id,
                CenarioICMS.cliente_id == tributo.cliente_id,
                CenarioICMS.produto_id == tributo.produto_id,
                CenarioICMS.status == 'producao'
            ).all()
            
            print(f"   Cenários disponíveis para este produto/cliente: {len(cenarios_disponiveis)}")
            for c in cenarios_disponiveis:
                print(f"     - ID={c.id}, CFOP={c.cfop}, Ativo={c.ativo}")

def verificar_resultados_auditoria():
    """Verifica resultados de auditoria recentes"""
    app = create_app()
    with app.app_context():
        print("\n=== VERIFICAÇÃO DE RESULTADOS DE AUDITORIA ===\n")
        
        # Buscar resultados recentes
        resultados = AuditoriaResultado.query.order_by(
            AuditoriaResultado.data_auditoria.desc()
        ).limit(5).all()
        
        if not resultados:
            print("❌ Nenhum resultado de auditoria encontrado!")
            return
        
        for resultado in resultados:
            print(f"📊 Resultado ID: {resultado.id}")
            print(f"   Tributo ID: {resultado.tributo_id}")
            print(f"   Cenário ID: {resultado.cenario_id}")
            print(f"   Tipo: {resultado.tipo_tributo}")
            print(f"   Status: {resultado.status}")
            print(f"   Valor Nota: R$ {resultado.valor_nota}")
            print(f"   Valor Calculado: R$ {resultado.valor_calculado}")
            print(f"   Data: {resultado.data_auditoria}")
            
            # Buscar informações do cenário usado
            if resultado.tipo_tributo == 'icms':
                cenario = CenarioICMS.query.get(resultado.cenario_id)
                if cenario:
                    print(f"   CFOP do Cenário: {cenario.cfop}")
            
            print("-" * 50)

if __name__ == "__main__":
    print("🔍 TESTE DA CORREÇÃO DO CFOP NA AUDITORIA FISCAL\n")
    
    verificar_tributos_com_cfop()
    verificar_cenarios_com_cfop()
    testar_busca_cenario_com_cfop()
    verificar_resultados_auditoria()
    
    print("\n✅ Verificação concluída!")
    print("\n📝 RESUMO DA CORREÇÃO:")
    print("1. ✅ Adicionado filtro de CFOP na busca de cenários")
    print("2. ✅ Carregamento do relacionamento nota_fiscal_item")
    print("3. ✅ Logs detalhados para diagnóstico")
    print("4. ✅ Verificação de correspondência de CFOP")
    print("\n🚀 A auditoria agora deve usar apenas cenários com CFOP correspondente!")
