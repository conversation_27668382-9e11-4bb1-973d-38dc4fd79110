"""
Sistema de gerenciamento de filas para controle de concorrência
"""
import threading
import queue
import time
import logging
from typing import Dict, Any, Callable, Optional
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class Task:
    id: str
    type: str
    user_id: int
    empresa_id: int
    data: Dict[str, Any]
    callback: Optional[Callable] = None
    priority: int = 1
    created_at: float = None
    started_at: float = None
    completed_at: float = None
    status: TaskStatus = TaskStatus.PENDING
    error: str = None
    result: Any = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

class QueueManager:
    """
    Gerenciador de filas para controlar a concorrência de operações pesadas
    """
    
    def __init__(self, max_workers: int = 4, max_queue_size: int = 100):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        
        # Filas por tipo de operação
        self.import_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.audit_queue = queue.PriorityQueue(maxsize=max_queue_size)
        
        # Pool de threads para cada tipo de operação
        self.import_executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="import")
        self.audit_executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="audit")
        
        # Controle de tarefas ativas
        self.active_tasks: Dict[str, Task] = {}
        self.task_lock = threading.RLock()
        
        # Controle de recursos por empresa (evitar sobrecarga por empresa)
        self.company_locks: Dict[int, threading.Semaphore] = {}
        self.company_lock = threading.Lock()
        
        # Estatísticas
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_imports': 0,
            'active_audits': 0
        }
        
        # Iniciar workers
        self._start_workers()
        
        logger.info(f"QueueManager iniciado com {max_workers} workers por tipo")

    def _start_workers(self):
        """Inicia os workers para processar as filas"""
        # Workers para importação
        for i in range(self.max_workers):
            thread = threading.Thread(
                target=self._import_worker,
                name=f"import-worker-{i}",
                daemon=True
            )
            thread.start()
        
        # Workers para auditoria
        for i in range(self.max_workers):
            thread = threading.Thread(
                target=self._audit_worker,
                name=f"audit-worker-{i}",
                daemon=True
            )
            thread.start()

    def _get_company_semaphore(self, empresa_id: int) -> threading.Semaphore:
        """Obtém ou cria um semáforo para controlar operações por empresa"""
        with self.company_lock:
            if empresa_id not in self.company_locks:
                # Máximo 2 operações simultâneas por empresa
                self.company_locks[empresa_id] = threading.Semaphore(2)
            return self.company_locks[empresa_id]

    def submit_import_task(self, task: Task) -> bool:
        """
        Submete uma tarefa de importação para a fila
        
        Returns:
            bool: True se a tarefa foi adicionada à fila, False se a fila está cheia
        """
        try:
            # Prioridade baseada no tamanho da tarefa (menor = maior prioridade)
            priority = task.priority
            
            # Adicionar à fila com prioridade
            self.import_queue.put((priority, time.time(), task), block=False)
            
            with self.task_lock:
                self.active_tasks[task.id] = task
                self.stats['total_tasks'] += 1
            
            logger.info(f"Tarefa de importação {task.id} adicionada à fila (prioridade: {priority})")
            return True
            
        except queue.Full:
            logger.warning(f"Fila de importação cheia. Tarefa {task.id} rejeitada")
            return False

    def submit_audit_task(self, task: Task) -> bool:
        """
        Submete uma tarefa de auditoria para a fila
        
        Returns:
            bool: True se a tarefa foi adicionada à fila, False se a fila está cheia
        """
        try:
            priority = task.priority
            
            self.audit_queue.put((priority, time.time(), task), block=False)
            
            with self.task_lock:
                self.active_tasks[task.id] = task
                self.stats['total_tasks'] += 1
            
            logger.info(f"Tarefa de auditoria {task.id} adicionada à fila (prioridade: {priority})")
            return True
            
        except queue.Full:
            logger.warning(f"Fila de auditoria cheia. Tarefa {task.id} rejeitada")
            return False

    def _import_worker(self):
        """Worker para processar tarefas de importação"""
        while True:
            try:
                # Obter próxima tarefa da fila
                priority, timestamp, task = self.import_queue.get(timeout=1)
                
                # Obter semáforo da empresa
                company_semaphore = self._get_company_semaphore(task.empresa_id)
                
                # Aguardar liberação do semáforo da empresa
                company_semaphore.acquire()
                
                try:
                    self._process_import_task(task)
                finally:
                    company_semaphore.release()
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Erro no worker de importação: {str(e)}")

    def _audit_worker(self):
        """Worker para processar tarefas de auditoria"""
        while True:
            try:
                priority, timestamp, task = self.audit_queue.get(timeout=1)
                
                company_semaphore = self._get_company_semaphore(task.empresa_id)
                company_semaphore.acquire()
                
                try:
                    self._process_audit_task(task)
                finally:
                    company_semaphore.release()
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Erro no worker de auditoria: {str(e)}")

    def _process_import_task(self, task: Task):
        """Processa uma tarefa de importação"""
        try:
            with self.task_lock:
                task.status = TaskStatus.RUNNING
                task.started_at = time.time()
                self.stats['active_imports'] += 1
            
            logger.info(f"Iniciando processamento da tarefa de importação {task.id}")
            
            # Executar a tarefa
            if task.callback:
                task.result = task.callback(task.data)
            
            with self.task_lock:
                task.status = TaskStatus.COMPLETED
                task.completed_at = time.time()
                self.stats['completed_tasks'] += 1
                self.stats['active_imports'] -= 1
            
            logger.info(f"Tarefa de importação {task.id} concluída")
            
        except Exception as e:
            with self.task_lock:
                task.status = TaskStatus.FAILED
                task.error = str(e)
                task.completed_at = time.time()
                self.stats['failed_tasks'] += 1
                self.stats['active_imports'] -= 1
            
            logger.error(f"Erro na tarefa de importação {task.id}: {str(e)}")

    def _process_audit_task(self, task: Task):
        """Processa uma tarefa de auditoria"""
        try:
            with self.task_lock:
                task.status = TaskStatus.RUNNING
                task.started_at = time.time()
                self.stats['active_audits'] += 1
            
            logger.info(f"Iniciando processamento da tarefa de auditoria {task.id}")
            
            if task.callback:
                task.result = task.callback(task.data)
            
            with self.task_lock:
                task.status = TaskStatus.COMPLETED
                task.completed_at = time.time()
                self.stats['completed_tasks'] += 1
                self.stats['active_audits'] -= 1
            
            logger.info(f"Tarefa de auditoria {task.id} concluída")
            
        except Exception as e:
            with self.task_lock:
                task.status = TaskStatus.FAILED
                task.error = str(e)
                task.completed_at = time.time()
                self.stats['failed_tasks'] += 1
                self.stats['active_audits'] -= 1
            
            logger.error(f"Erro na tarefa de auditoria {task.id}: {str(e)}")

    def get_task_status(self, task_id: str) -> Optional[Task]:
        """Obtém o status de uma tarefa"""
        with self.task_lock:
            return self.active_tasks.get(task_id)

    def cancel_task(self, task_id: str) -> bool:
        """Cancela uma tarefa (se ainda não iniciada)"""
        with self.task_lock:
            task = self.active_tasks.get(task_id)
            if task and task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
                return True
            return False

    def get_queue_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas das filas"""
        return {
            'import_queue_size': self.import_queue.qsize(),
            'audit_queue_size': self.audit_queue.qsize(),
            'stats': self.stats.copy(),
            'active_tasks': len([t for t in self.active_tasks.values() if t.status == TaskStatus.RUNNING])
        }

    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """Remove tarefas antigas da memória"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        with self.task_lock:
            to_remove = []
            for task_id, task in self.active_tasks.items():
                if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                    task.completed_at and 
                    current_time - task.completed_at > max_age_seconds):
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self.active_tasks[task_id]
            
            if to_remove:
                logger.info(f"Removidas {len(to_remove)} tarefas antigas da memória")

# Instância global do gerenciador de filas
_queue_manager = None

def get_queue_manager() -> QueueManager:
    """Obtém a instância global do gerenciador de filas"""
    global _queue_manager
    if _queue_manager is None:
        _queue_manager = QueueManager()
    return _queue_manager

def init_queue_manager(max_workers: int = 4, max_queue_size: int = 100) -> QueueManager:
    """Inicializa o gerenciador de filas"""
    global _queue_manager
    _queue_manager = QueueManager(max_workers, max_queue_size)
    return _queue_manager
