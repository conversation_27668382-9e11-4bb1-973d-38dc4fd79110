from .escritorio import db
from sqlalchemy.sql import func
import json

class AuditoriaSumario(db.Model):
    __tablename__ = 'auditoria_sumario'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.<PERSON>umn(db.Integer, db.<PERSON>ey('escritorio.id'), nullable=False)
    ano = db.Column(db.Integer, nullable=False)
    mes = db.Column(db.Integer, nullable=False)
    tipo_tributo = db.Column(db.String(20), nullable=False)  # 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'
    total_notas = db.Column(db.Integer, nullable=False, default=0)
    total_produtos = db.Column(db.Integer, nullable=False, default=0)
    valor_total_notas = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # Soma dos valores dos tributos das notas fiscais
    valor_total_cenarios = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # Soma dos valores dos tributos calculados pelos cenários
    valor_total_produtos = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # Soma dos valores dos produtos (para compatibilidade)
    valor_total_tributo = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # Igual ao valor_total_cenarios (para compatibilidade)
    total_conforme = db.Column(db.Integer, nullable=False, default=0)
    total_inconsistente = db.Column(db.Integer, nullable=False, default=0)
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())
    notas_contabilizadas = db.Column(db.Text, nullable=True)  # Armazena lista de números de notas como JSON
    produtos_contabilizados = db.Column(db.Text, nullable=True)  # Armazena lista de IDs de produtos como JSON
    valor_inconsistente_maior = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # Valor inconsistente a maior
    valor_inconsistente_menor = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # Valor inconsistente a menor

    # Relacionamentos
    empresa = db.relationship('Empresa', backref='auditoria_sumarios')
    escritorio = db.relationship('Escritorio', backref='auditoria_sumarios')

    def __repr__(self):
        return f"<AuditoriaSumario {self.id} - {self.tipo_tributo} - {self.ano}/{self.mes}>"

    def get_notas_contabilizadas(self):
        """Retorna o conjunto de notas contabilizadas"""
        if not self.notas_contabilizadas:
            return set()
        return set(json.loads(self.notas_contabilizadas))

    def set_notas_contabilizadas(self, notas_set):
        """Define o conjunto de notas contabilizadas"""
        self.notas_contabilizadas = json.dumps(list(notas_set))

    def get_produtos_contabilizados(self):
        """Retorna o conjunto de produtos contabilizados"""
        if not self.produtos_contabilizados:
            return set()
        return set(json.loads(self.produtos_contabilizados))

    def set_produtos_contabilizados(self, produtos_set):
        """Define o conjunto de produtos contabilizados"""
        self.produtos_contabilizados = json.dumps(list(produtos_set))

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'ano': self.ano,
            'mes': self.mes,
            'tipo_tributo': self.tipo_tributo,
            'total_notas': self.total_notas,
            'total_produtos': self.total_produtos,
            'valor_total_notas': float(self.valor_total_notas) if self.valor_total_notas else 0,
            'valor_total_cenarios': float(self.valor_total_cenarios) if self.valor_total_cenarios else 0,
            'valor_total_produtos': float(self.valor_total_produtos) if self.valor_total_produtos else 0,
            'valor_total_tributo': float(self.valor_total_tributo) if self.valor_total_tributo else 0,
            'total_conforme': self.total_conforme,
            'total_inconsistente': self.total_inconsistente,
            'valor_inconsistente_maior': float(self.valor_inconsistente_maior) if self.valor_inconsistente_maior else 0,
            'valor_inconsistente_menor': float(self.valor_inconsistente_menor) if self.valor_inconsistente_menor else 0,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }
