#!/usr/bin/env python3
"""
Script para testar as correções na auditoria fiscal
"""

import sys
import os
sys.path.append('./back')

from models import db, AuditoriaSumario, AuditoriaResultado, Tributo
from app import create_app
from sqlalchemy import text

def verificar_sumarios():
    """Verifica os sumários de auditoria existentes"""
    app = create_app()
    with app.app_context():
        print("=== VERIFICAÇÃO DOS SUMÁRIOS DE AUDITORIA ===\n")
        
        # Buscar todos os sumários
        sumarios = AuditoriaSumario.query.all()
        
        if not sumarios:
            print("❌ Nenhum sumário encontrado!")
            return
        
        for sumario in sumarios:
            print(f"📊 Sumário ID: {sumario.id}")
            print(f"   Empresa: {sumario.empresa_id}")
            print(f"   Tipo: {sumario.tipo_tributo}")
            print(f"   Período: {sumario.mes}/{sumario.ano}")
            print(f"   Total Notas: {sumario.total_notas}")
            print(f"   Total Produtos: {sumario.total_produtos}")
            print(f"   Valor Total Notas: R$ {sumario.valor_total_notas}")
            
            # Verificar se tem a nova coluna
            if hasattr(sumario, 'valor_total_cenarios'):
                print(f"   Valor Total Cenários: R$ {sumario.valor_total_cenarios}")
            else:
                print("   ❌ Coluna 'valor_total_cenarios' não encontrada!")
            
            print(f"   Valor Total Tributo: R$ {sumario.valor_total_tributo}")
            print(f"   Conforme: {sumario.total_conforme}")
            print(f"   Inconsistente: {sumario.total_inconsistente}")
            print(f"   Inconsistente Maior: R$ {sumario.valor_inconsistente_maior}")
            print(f"   Inconsistente Menor: R$ {sumario.valor_inconsistente_menor}")
            print("-" * 50)

def verificar_estrutura_tabela():
    """Verifica se a nova coluna foi adicionada corretamente"""
    app = create_app()
    with app.app_context():
        print("\n=== VERIFICAÇÃO DA ESTRUTURA DA TABELA ===\n")
        
        try:
            # Verificar se a coluna existe
            result = db.session.execute(text("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'auditoria_sumario' 
                AND column_name = 'valor_total_cenarios'
            """)).fetchone()
            
            if result:
                print("✅ Coluna 'valor_total_cenarios' encontrada!")
                print(f"   Tipo: {result[1]}")
                print(f"   Nullable: {result[2]}")
                print(f"   Default: {result[3]}")
            else:
                print("❌ Coluna 'valor_total_cenarios' NÃO encontrada!")
                
        except Exception as e:
            print(f"❌ Erro ao verificar estrutura: {e}")

def verificar_tributos_exemplo():
    """Verifica alguns tributos para entender os dados"""
    app = create_app()
    with app.app_context():
        print("\n=== VERIFICAÇÃO DE TRIBUTOS (EXEMPLO) ===\n")
        
        # Buscar alguns tributos
        tributos = Tributo.query.limit(5).all()
        
        for tributo in tributos:
            print(f"🧾 Tributo ID: {tributo.id}")
            print(f"   Empresa: {tributo.empresa_id}")
            print(f"   Cliente: {tributo.cliente_id}")
            print(f"   Produto: {tributo.produto_id}")
            print(f"   Valor Total: R$ {tributo.valor_total}")
            print(f"   ICMS Nota: R$ {tributo.icms_valor}")
            print(f"   ICMS Cenário: R$ {tributo.cenario_icms_valor}")
            print(f"   Data Emissão: {tributo.data_emissao}")
            print("-" * 30)

if __name__ == "__main__":
    print("🔍 TESTE DAS CORREÇÕES NA AUDITORIA FISCAL\n")
    
    verificar_estrutura_tabela()
    verificar_sumarios()
    verificar_tributos_exemplo()
    
    print("\n✅ Verificação concluída!")
    print("\n📝 PRÓXIMOS PASSOS:")
    print("1. Execute uma auditoria pelo frontend")
    print("2. Verifique se os valores estão corretos")
    print("3. Compare 'valor_total_notas' vs 'valor_total_cenarios'")
