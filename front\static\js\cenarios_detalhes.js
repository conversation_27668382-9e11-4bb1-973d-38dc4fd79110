/**
 * cenarios_detalhes.js - Auditoria Fiscal
 * Funções para gerenciar as páginas de detalhes de cenários de tributos
 */

// Variáveis globais - usando namespace para evitar conflitos
window.cenariosDetalhes = window.cenariosDetalhes || {
  // Múltiplas referências de tabelas por status
  cenarioTables: {
    novo: null,
    producao: null,
    inconsistente: null,
  },

  // Manter compatibilidade com código existente
  get cenarioTable() {
    return this.cenarioTables[this.currentStatus] || this.cenarioTables.novo;
  },
  set cenarioTable(value) {
    if (this.currentStatus) {
      this.cenarioTables[this.currentStatus] = value;
    } else {
      this.cenarioTables.novo = value;
    }
  },

  selectedCompany: null,
  selectedYear: null,
  selectedMonth: null,
  currentTipoTributo: null,
  currentDirecao: null,
  currentStatus: 'novo',

  // Armazenar cenários selecionados por status
  selectedCenarios: {
    novo: [],
    producao: [],
    inconsistente: [],
  },

  // Cache de dados para evitar recarregamento
  cachedData: {
    novo: null,
    producao: null,
    inconsistente: null,
  },

  // Flag para indicar se está carregando
  isLoading: false,

  // Timeout para controle de foco dos filtros
  focusRestoreTimeout: null,

  // Funções globais que podem ser acessadas de outros arquivos
  updateCenarioStatus: null, // Será definida mais tarde
  ativarCenario: null, // Será definida mais tarde
  desativarCenario: null, // Será definida mais tarde
  atualizarVigenciaCenario: null, // Será definida mais tarde
};

// Adicionar um listener para o evento de mudança de empresa
window.addEventListener('company-changed', function (event) {
  if (event.detail && event.detail.companyId) {
    // Limpar o cache de dados ao mudar de empresa
    window.cenariosDetalhes.cachedData = {
      novo: null,
      producao: null,
      inconsistente: null,
    };

    // Limpar seleções
    window.cenariosDetalhes.selectedCenarios = {
      novo: [],
      producao: [],
      inconsistente: [],
    };

    // Atualizar a empresa selecionada
    window.cenariosDetalhes.selectedCompany = event.detail.companyId;

    // Recarregar dados se estivermos na página de cenários
    if (window.location.pathname.includes('/cenarios/')) {
      // Forçar recarregamento dos dados sem usar cache
      loadCenarioData(window.cenariosDetalhes.currentStatus, 1, 100, true);

      // Forçar atualização dos contadores
      loadCenariosCounts();
    }
  }
});

// Adicionar um listener para o evento de toggle da sidebar
document.addEventListener('sidebar-toggle', function () {
  if (window.cenariosDetalhes.cenarioTable) {
    setTimeout(() => {
      window.cenariosDetalhes.cenarioTable.columns.adjust().draw(false);
    }, 300); // Aguardar a animação da sidebar terminar
  }
});

document.addEventListener('DOMContentLoaded', function () {
  // Verificar se estamos em uma página de detalhes de cenários
  const path = window.location.pathname;

  if (path.startsWith('/cenarios/')) {
    setupCenariosDetalhesPage();

    // Garantir que os dropdowns estejam fechados se a sidebar estiver colapsada
    setTimeout(() => {
      if (typeof ensureDropdownsClosedWhenCollapsed === 'function') {
        ensureDropdownsClosedWhenCollapsed();
      }
    }, 100);
  }
});

/**
 * Configura a página de detalhes de cenários
 */
function setupCenariosDetalhesPage() {
  // Obter informações da URL
  const path = window.location.pathname;
  const parts = path.split('/');

  if (parts.length >= 4) {
    window.cenariosDetalhes.currentDirecao = parts[2]; // entrada ou saida
    window.cenariosDetalhes.currentTipoTributo = parts[3]; // icms, icms_st, ipi, pis, cofins, difal

    // Limpar o conteúdo atual
    const pageContent = document.getElementById('page-content');
    if (pageContent) {
      // Esconder todas as páginas existentes
      const pages = document.querySelectorAll('.page-section');
      pages.forEach((page) => {
        page.classList.remove('active');
      });
    } else {
    }

    // Carregar o conteúdo da página
    loadCenariosDetalhesContent();

    // Configurar filtros
    setupFilters();

    // Aguardar um pouco para garantir que os filtros estejam configurados
    setTimeout(() => {
      console.log(
        'Iniciando carregamento de dados após configuração dos filtros',
      );
      // Não chamar loadCenarioData() aqui, pois setupTabEvents() já fará isso
    }, 100);
  } else {
  }
}

/**
 * Carrega o conteúdo da página de detalhes de cenários
 */
function loadCenariosDetalhesContent() {
  const pageContent = document.getElementById('page-content');
  if (!pageContent) {
    return;
  }

  // Verificar se a seção já existe
  let cenariosDetalhesSection = document.getElementById(
    'page-cenarios-detalhes',
  );
  if (cenariosDetalhesSection) {
    cenariosDetalhesSection.classList.add('active');
    return;
  }

  // Adicionar estilos para a tabela de comparação e coluna NCM
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    .comparison-row td {
      padding: 0 !important;
      background-color: #f8f9fa;
    }
    .comparison-content {
      padding: 15px;
      border-top: 1px solid #dee2e6;
    }
    .table-danger {
      background-color: rgba(220, 53, 69, 0.1) !important;
    }
    .text-danger.fw-bold {
      text-decoration: underline;
    }
    .expand-comparison-btn {
      margin-right: 5px;
    }
    .inconsistente-row {
      border-bottom: 1px solid #dee2e6;
    }
    .comparison-row table {
      margin-bottom: 0 !important;
    }
    .comparison-row .table-primary {
      background-color: rgba(13, 110, 253, 0.1) !important;
    }
    .producao-row {
      display: flex;
      width: 100%;
      background-color: rgba(13, 110, 253, 0.1);
      padding: 8px;
      border-radius: 4px;
    }
    .producao-row > div {
      padding: 0 8px;
      display: flex;
      align-items: center;
    }
    .action-cell {
      margin-left: auto;
    }
    .inconsistent-value {
      color: #dc3545 !important;
      font-weight: bold !important;
      text-decoration: underline !important;
    }
    .actions-container {
      display: flex;
      align-items: center;
    }
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    .loading-spinner {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      text-align: center;
    }
    .loading-spinner p {
      margin-top: 10px;
      font-weight: bold;
    }
    .table tbody tr.selected {
      background-color: rgba(13, 110, 253, 0.1) !important;
    }
    .cenario-producao-row {
      background-color: rgba(13, 110, 253, 0.1) !important;
    }
    .cenario-producao-row td {
      border-top: none !important;
    }
    .cenario-producao-badge {
      font-size: 0.7rem;
      padding: 0.2rem 0.5rem;
      margin-right: 0.5rem;
      background-color: #0d6efd;
      color: white;
      border-radius: 3px;
    }
    .select-checkbox {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
    .select-all-checkbox {
      margin-right: 5px;
    }
    .batch-actions-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .batch-actions-container .btn {
      margin-left: 10px;
    }
    .selection-info {
      font-weight: bold;
      color: #0d6efd;
    }
    .progress-container {
      margin-top: 10px;
      width: 100%;
    }
  `;
  document.head.appendChild(styleElement);

  // Criar a seção de detalhes de cenários
  cenariosDetalhesSection = document.createElement('div');
  cenariosDetalhesSection.id = 'page-cenarios-detalhes';
  cenariosDetalhesSection.className = 'page-section active';

  // Título da página
  const titulo = getTituloTributo();

  // Conteúdo HTML da página
  cenariosDetalhesSection.innerHTML = `
    <div class="section-header">
      <h2><i class="fas fa-sitemap"></i> Cenários de ${
        window.cenariosDetalhes.currentDirecao === 'entrada'
          ? 'Entrada'
          : 'Saída'
      } - ${titulo}</h2>
    </div>

    <!-- Cards de contagem de cenários -->
    <div class="row mb-4" id="cenarios-count-cards">
      <div class="col-md-4">
        <div class="card shadow-sm border-0 h-100">
          <div class="card-body d-flex align-items-center">
            <div class="card-icon bg-primary rounded-circle me-3">
              <i class="fas fa-plus-circle"></i>
            </div>
            <div>
              <h6 class="card-subtitle mb-1">Cenários Novos</h6>
              <h3 class="card-title mb-0" id="count-novos">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </h3>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card shadow-sm border-0 h-100">
          <div class="card-body d-flex align-items-center">
            <div class="card-icon bg-success rounded-circle me-3">
              <i class="fas fa-check-circle"></i>
            </div>
            <div>
              <h6 class="card-subtitle mb-1">Cenários em Produção</h6>
              <h3 class="card-title mb-0" id="count-producao">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </h3>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card shadow-sm border-0 h-100">
          <div class="card-body d-flex align-items-center">
            <div class="card-icon bg-warning rounded-circle me-3">
              <i class="fas fa-exclamation-circle"></i>
            </div>
            <div>
              <h6 class="card-subtitle mb-1">Cenários Inconsistentes</h6>
              <h3 class="card-title mb-0" id="count-inconsistentes">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="tabs-container">
      <ul class="nav nav-tabs" id="tributo-tabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="novos-tab" data-bs-toggle="tab" data-bs-target="#novos-content" type="button" role="tab" aria-controls="novos-content" aria-selected="true">
            Novos
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="producao-tab" data-bs-toggle="tab" data-bs-target="#producao-content" type="button" role="tab" aria-controls="producao-content" aria-selected="false">
            Produção
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="inconsistentes-tab" data-bs-toggle="tab" data-bs-target="#inconsistentes-content" type="button" role="tab" aria-controls="inconsistentes-content" aria-selected="false">
            Inconsistentes
          </button>
        </li>

      </ul>

      <div class="tab-content" id="tributo-tabs-content">
        <div class="tab-pane fade show active" id="novos-content" role="tabpanel" aria-labelledby="novos-tab">
          <div class="card">
            <div class="card-body">
              <div id="novos-table-container">
                <div class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Carregando...</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="producao-content" role="tabpanel" aria-labelledby="producao-tab">
          <div class="card">
            <div class="card-body">
              <div id="producao-table-container">
                <div class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Carregando...</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="inconsistentes-content" role="tabpanel" aria-labelledby="inconsistentes-tab">
          <div class="card">
            <div class="card-body">
              <div id="inconsistentes-table-container">
                <div class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Carregando...</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  `;

  // Adicionar a seção ao conteúdo da página
  pageContent.appendChild(cenariosDetalhesSection);

  // Configurar eventos para as abas
  setupTabEvents();

  // Adicionar botão de reverificação de status
  addReverificarStatusButton();

  // Adicionar botão de enviar para produção em massa na aba Novos
  addBulkSendToProductionButton();

  // Carregar contagens de cenários
  loadCenariosCounts();
}

/**
 * Adiciona o botão de reverificação de status na interface
 */
function addReverificarStatusButton() {
  // Adicionar o botão na seção de header
  const sectionHeader = document.querySelector('.section-header');
  if (!sectionHeader) return;

  // Verificar se o botão já existe
  if (document.getElementById('reverificar-status-btn')) return;

  // Adicionar classe para flexbox
  sectionHeader.classList.add(
    'd-flex',
    'justify-content-between',
    'align-items-center',
  );

  // Criar container para os botões
  let actionsContainer = document.querySelector('.actions-container');
  if (!actionsContainer) {
    actionsContainer = document.createElement('div');
    actionsContainer.className = 'actions-container d-flex';
    sectionHeader.appendChild(actionsContainer);
  }

  // Criar o botão
  const button = document.createElement('button');
  button.id = 'reverificar-status-btn';
  button.className = 'btn btn-primary me-2';
  button.innerHTML = '<i class="fas fa-sync-alt"></i> Reverificar Status';

  // Adicionar o botão ao container de ações
  actionsContainer.appendChild(button);

  // Adicionar evento de clique
  button.addEventListener('click', function () {
    reverificarStatusTributos();
  });
}

/**
 * Reverifica o status de todos os cenários para a empresa selecionada
 */
function reverificarStatusTributos() {
  // Verificar se há uma empresa selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert('Selecione uma empresa para reverificar os status dos cenários.');
    return;
  }

  // Mostrar confirmação
  if (
    !confirm(
      'Deseja reverificar o status de todos os cenários para a empresa selecionada? Esta operação pode levar algum tempo.',
    )
  ) {
    return;
  }

  // Mostrar indicador de carregamento
  const button = document.getElementById('reverificar-status-btn');
  const originalText = button.innerHTML;
  button.disabled = true;
  button.innerHTML =
    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processando...';

  // Preparar dados para a requisição
  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = currentUser.escritorio_id;

  const data = {
    empresa_id: parseInt(window.cenariosDetalhes.selectedCompany),
    escritorio_id: escritorioId,
    tipo_tributo: window.cenariosDetalhes.currentTipoTributo,
  };

  // Fazer requisição para a API
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/reverificar-status`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(data),
    },
  )
    .then((response) => response.json())
    .then((data) => {
      // Restaurar o botão
      button.disabled = false;
      button.innerHTML = originalText;

      // Mostrar resultado
      if (data.success) {
        alert(data.message || 'Status dos cenários reverificados com sucesso!');

        // Recarregar os dados para todas as abas
        loadCenarioData('novo');
        loadCenarioData('producao');
        loadCenarioData('inconsistente');
      } else {
        alert(data.message || 'Erro ao reverificar status dos cenários.');
      }
    })
    .catch((error) => {
      // Restaurar o botão
      button.disabled = false;
      button.innerHTML = originalText;

      alert(`Erro ao reverificar status dos cenários: ${error.message}`);
    });
}

/**
 * Aguarda a empresa estar disponível e carrega os dados
 */
function waitForCompanyAndLoadData() {
  let attempts = 0;
  const maxAttempts = 10;

  function checkCompany() {
    attempts++;

    // Sincronizar com o valor mais recente da empresa selecionada
    const storedCompany = localStorage.getItem('selectedCompany');
    const headerCompanySelect = document.getElementById('company-select');

    // Priorizar o valor do seletor do header, depois localStorage, depois a variável global
    if (headerCompanySelect && headerCompanySelect.value) {
      window.cenariosDetalhes.selectedCompany = headerCompanySelect.value;
    } else if (storedCompany) {
      window.cenariosDetalhes.selectedCompany = storedCompany;
    } else if (window.selectedCompany) {
      window.cenariosDetalhes.selectedCompany = window.selectedCompany;
    }

    console.log(
      `Tentativa ${attempts}: Empresa selecionada:`,
      window.cenariosDetalhes.selectedCompany,
    );

    // Se encontrou a empresa, carregar os dados
    if (window.cenariosDetalhes.selectedCompany) {
      loadAllCenarioData();
      return;
    }

    // Se não encontrou e ainda há tentativas, tentar novamente
    if (attempts < maxAttempts) {
      setTimeout(checkCompany, 200);
    } else {
      // Após todas as tentativas, mostrar alerta
      toggleLoadingOverlay(false);
      alert('Por favor, selecione uma empresa para visualizar os cenários.');
    }
  }

  checkCompany();
}

/**
 * Carrega todos os dados de cenários de uma vez para todas as abas
 */
function loadAllCenarioData() {
  console.log(
    'Iniciando carregamento de todos os cenários para empresa:',
    window.cenariosDetalhes.selectedCompany,
  );

  // Verificar se há uma empresa selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    toggleLoadingOverlay(false);
    alert('Por favor, selecione uma empresa para visualizar os cenários.');
    return;
  }

  // Contador para controlar quando todos os dados foram carregados
  let loadedTabs = 0;
  let renderedTabs = 0;
  const totalTabs = 3; // novo, producao, inconsistente

  // Função para verificar se todos os dados foram carregados
  function checkAllLoaded() {
    loadedTabs++;
    console.log(`Dados carregados: ${loadedTabs}/${totalTabs}`);

    if (loadedTabs >= totalTabs) {
      console.log(
        'Todos os dados foram carregados, aguardando renderização...',
      );
      // Aguardar um pouco mais para garantir que a renderização termine
      setTimeout(() => {
        checkAllRendered();
      }, 200);
    }
  }

  // Função para verificar se todos os dados foram renderizados
  function checkAllRendered() {
    renderedTabs++;
    console.log(`Tabelas renderizadas: ${renderedTabs}/${totalTabs}`);

    if (renderedTabs >= totalTabs) {
      toggleLoadingOverlay(false);
      console.log(
        'Todos os cenários foram carregados e renderizados com sucesso',
      );
    }
  }

  // Carregar dados para cada status usando o novo parâmetro load_all
  ['novo', 'producao', 'inconsistente'].forEach((status, index) => {
    setTimeout(() => {
      loadCenarioDataWithLoadAll(status, checkAllLoaded, checkAllRendered);
    }, index * 100); // Pequeno delay entre as requisições
  });
}

/**
 * Configura eventos para as abas
 */
function setupTabEvents() {
  const tabs = document.querySelectorAll('#tributo-tabs .nav-link');

  // Adicionar overlay de carregamento inicial
  toggleLoadingOverlay(
    true,
    'Carregando todos os cenários, por favor aguarde...',
  );

  // Aguardar um pouco para garantir que a empresa esteja carregada
  setTimeout(() => {
    // Aguardar a empresa estar disponível e carregar os dados
    waitForCompanyAndLoadData();
  }, 500);

  tabs.forEach((tab) => {
    tab.addEventListener('click', function (event) {
      const tabId = event.target.id;
      let status = tabId.split('-')[0]; // novos, producao, inconsistentes

      // Corrigir o status para o formato esperado pela API
      if (status === 'inconsistentes') {
        status = 'inconsistente'; // Converter para singular conforme esperado pela API
      } else if (status === 'novos') {
        status = 'novo'; // Garantir consistência
      } else if (status === 'producao') {
        status = 'producao'; // Manter como está
      }

      // Atualizar o status atual
      window.cenariosDetalhes.currentStatus = status;

      // Usar dados em cache se disponíveis, caso contrário carregar novos dados
      if (window.cenariosDetalhes.cachedData[status]) {
        const container = document.getElementById(
          `${
            status === 'inconsistente' ? 'inconsistentes' : status
          }-table-container`,
        );
        if (container) {
          const cachedData = window.cenariosDetalhes.cachedData[status];

          // Se for status inconsistente, verificar se temos os cenários em produção correspondentes
          if (status === 'inconsistente') {
            if (window.cenariosDetalhes.producaoCenarios) {
              renderCenarioTable(
                container,
                cachedData.cenarios,
                status,
                cachedData.pagination,
              );
            } else {
              loadProducaoCenariosForInconsistentes(cachedData.cenarios, () => {
                renderCenarioTable(
                  container,
                  cachedData.cenarios,
                  status,
                  cachedData.pagination,
                );
              });
            }
          } else {
            renderCenarioTable(
              container,
              cachedData.cenarios,
              status,
              cachedData.pagination,
            );
          }
        }
      } else {
        // Carregar dados se não estiverem em cache
        loadCenarioData(status, 1, 10000, false);
      }
    });
  });
}

/**
 * Configura os filtros da página
 */
function setupFilters() {
  console.log('Configurando filtros...');

  // Obter os filtros do header e da variável global
  // Usar a variável global selectedCompany definida em common.js ou dashboard.js
  // Priorizar o valor do localStorage, que é o mais atualizado
  const storedCompany = localStorage.getItem('selectedCompany');
  window.cenariosDetalhes.selectedCompany =
    storedCompany || window.selectedCompany;

  console.log('Empresa do localStorage:', storedCompany);
  console.log('Empresa da variável global:', window.selectedCompany);

  // Sincronizar com o valor do seletor de empresa no header
  const companySelect = document.getElementById('company-select');
  if (companySelect && companySelect.value) {
    window.cenariosDetalhes.selectedCompany = companySelect.value;
    console.log('Empresa do seletor do header:', companySelect.value);
  } else {
    console.log('Seletor de empresa não encontrado ou sem valor');
  }

  console.log(
    'Empresa final selecionada:',
    window.cenariosDetalhes.selectedCompany,
  );

  // Obter os valores dos seletores de ano e mês
  window.cenariosDetalhes.selectedYear =
    document.getElementById('year-select')?.value;
  window.cenariosDetalhes.selectedMonth =
    document.getElementById('month-select')?.value;

  // Adicionar event listeners para os filtros
  if (companySelect) {
    companySelect.addEventListener('change', function () {
      window.cenariosDetalhes.selectedCompany = this.value;
      loadCenarioData();
    });
  }

  const yearSelect = document.getElementById('year-select');
  if (yearSelect) {
    yearSelect.addEventListener('change', function () {
      window.cenariosDetalhes.selectedYear = this.value;
      loadCenarioData();
    });
  }

  const monthSelect = document.getElementById('month-select');
  if (monthSelect) {
    monthSelect.addEventListener('change', function () {
      window.cenariosDetalhes.selectedMonth = this.value;
      loadCenarioData();
    });
  }
}

/**
 * Carrega as contagens de cenários para os cards
 */
function loadCenariosCounts() {
  // Verificar se há uma empresa selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    return;
  }

  // Parâmetros para a requisição
  const params = new URLSearchParams();
  params.append('empresa_id', window.cenariosDetalhes.selectedCompany);
  params.append('direcao', window.cenariosDetalhes.currentDirecao);

  // Adicionar filtros de ano e mês se estiverem definidos
  if (window.cenariosDetalhes.selectedYear) {
    params.append('year', window.cenariosDetalhes.selectedYear);
  }

  if (window.cenariosDetalhes.selectedMonth) {
    params.append('month', window.cenariosDetalhes.selectedMonth);
  }

  // Fazer requisição para a API
  fetch(
    `/api/cenarios/${
      window.cenariosDetalhes.currentTipoTributo
    }/count?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Atualizar os contadores nos cards
        document.getElementById('count-novos').textContent =
          data.counts.novo || 0;
        document.getElementById('count-producao').textContent =
          data.counts.producao || 0;
        document.getElementById('count-inconsistentes').textContent =
          data.counts.inconsistente || 0;
      } else {
        document.getElementById('count-novos').textContent = '0';
        document.getElementById('count-producao').textContent = '0';
        document.getElementById('count-inconsistentes').textContent = '0';
      }
    })
    .catch((error) => {
      document.getElementById('count-novos').textContent = '0';
      document.getElementById('count-producao').textContent = '0';
      document.getElementById('count-inconsistentes').textContent = '0';
    });
}

/**
 * Adiciona o botão de enviar para produção em massa na aba Novos
 */
function addBulkSendToProductionButton() {
  // Adicionar o botão no container da tabela de novos cenários
  const container = document.getElementById('novos-table-container');
  if (!container) return;

  // Criar o botão
  const buttonContainer = document.createElement('div');
  buttonContainer.className = 'd-flex justify-content-end mb-3';
  buttonContainer.innerHTML = `
    <button id="bulk-send-to-production-btn" class="btn btn-success">
      <i class="fas fa-check-circle me-2"></i>Enviar Cenários Filtrados para Produção
    </button>
  `;

  // Inserir o botão antes do conteúdo existente
  container.insertBefore(buttonContainer, container.firstChild);

  // Adicionar evento de clique
  document
    .getElementById('bulk-send-to-production-btn')
    .addEventListener('click', function () {
      bulkSendToProduction();
    });
}

/**
 * Atualiza o status de múltiplos cenários em lote
 * @param {string} currentStatus - Status atual dos cenários
 * @param {string} newStatus - Novo status para os cenários
 */
function batchUpdateStatus(currentStatus, newStatus) {
  // Verificar se há cenários selecionados
  const selectedCenarios =
    window.cenariosDetalhes.selectedCenarios[currentStatus];
  if (!selectedCenarios || selectedCenarios.length === 0) {
    alert('Nenhum cenário selecionado.');
    return;
  }

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert(
      'Por favor, selecione uma empresa antes de atualizar o status dos cenários.',
    );
    return;
  }

  // Confirmar a operação
  const statusText = newStatus === 'producao' ? 'produção' : newStatus;
  if (
    !confirm(
      `Deseja realmente enviar ${selectedCenarios.length} cenários para ${statusText}?`,
    )
  ) {
    return;
  }

  // Mostrar overlay de carregamento
  toggleLoadingOverlay(
    true,
    `Atualizando status de ${selectedCenarios.length} cenários para ${statusText}...`,
  );

  // Obter o ID da empresa e do escritório
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = parseInt(currentUser.escritorio_id || '0');

  // Verificar se os IDs são válidos
  if (isNaN(empresaId) || empresaId <= 0) {
    alert('ID da empresa inválido. Por favor, selecione uma empresa válida.');
    toggleLoadingOverlay(false);
    return;
  }

  if (isNaN(escritorioId) || escritorioId <= 0) {
    alert('ID do escritório não encontrado. Por favor, faça login novamente.');
    toggleLoadingOverlay(false);
    return;
  }

  // Preparar dados para a requisição
  const data = {
    empresa_id: empresaId,
    escritorio_id: escritorioId,
    status: newStatus,
    cenario_ids: selectedCenarios,
  };

  // Para status 'producao', não enviamos a data de início de vigência
  // O backend vai buscar a data de emissão da nota fiscal para cada cenário
  // Isso garante que o comportamento seja igual ao do botão individual

  // Fazer requisição para a API
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/bulk-status`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(data),
    },
  )
    .then((response) => response.json())
    .then((data) => {
      // Esconder overlay de carregamento
      toggleLoadingOverlay(false);

      if (data.success) {
        // Mostrar mensagem de sucesso
        alert(
          `${
            data.updated_count || selectedCenarios.length
          } cenários foram atualizados para ${statusText} com sucesso!`,
        );

        // Limpar seleções
        window.cenariosDetalhes.selectedCenarios[currentStatus] = [];

        // Recarregar os dados para todas as abas
        loadCenarioData('novo', 1, 100, true);
        loadCenarioData('producao', 1, 100, true);
        loadCenarioData('inconsistente', 1, 100, true);

        // Atualizar contagens
        loadCenariosCounts();
      } else {
        alert(data.message || `Erro ao atualizar cenários para ${statusText}.`);
      }
    })
    .catch((error) => {
      // Esconder overlay de carregamento
      toggleLoadingOverlay(false);

      console.error('Erro ao atualizar status dos cenários:', error);
      alert(`Erro ao atualizar status dos cenários: ${error.message}`);
    });
}

/**
 * Envia múltiplos cenários para produção com base nos filtros atuais
 */
function bulkSendToProduction() {
  // Verificar se há uma empresa selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert('Selecione uma empresa para enviar cenários para produção.');
    return;
  }

  // Obter a tabela de cenários
  const table = window.cenariosDetalhes.cenarioTable;
  if (!table) {
    alert('Nenhum cenário disponível para enviar para produção.');
    return;
  }

  // Obter os dados filtrados da tabela
  const filteredData = table.rows({ search: 'applied' }).data().toArray();
  if (filteredData.length === 0) {
    alert('Nenhum cenário encontrado com os filtros atuais.');
    return;
  }

  // Confirmar a ação
  if (
    !confirm(
      `Deseja enviar ${filteredData.length} cenários para produção? Esta ação não pode ser desfeita.`,
    )
  ) {
    return;
  }

  // Obter IDs dos cenários filtrados
  const cenarioIds = filteredData.map((row) => row.id);

  // Preparar dados para a requisição
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = currentUser.escritorio_id;

  const data = {
    empresa_id: parseInt(window.cenariosDetalhes.selectedCompany),
    escritorio_id: escritorioId,
    cenario_ids: cenarioIds,
    status: 'producao',
    // Não enviamos a data de início de vigência para que o backend busque a data de emissão da nota fiscal
  };

  // Mostrar indicador de carregamento
  const button = document.getElementById('bulk-send-to-production-btn');
  const originalText = button.innerHTML;
  button.disabled = true;
  button.innerHTML =
    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processando...';

  // Fazer requisição para a API
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/bulk-status`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(data),
    },
  )
    .then((response) => response.json())
    .then((data) => {
      // Restaurar o botão
      button.disabled = false;
      button.innerHTML = originalText;

      // Mostrar resultado
      if (data.success) {
        alert(
          `${
            data.updated_count || 0
          } cenários foram enviados para produção com sucesso!`,
        );

        // Recarregar os dados para todas as abas
        loadCenarioData('novo');
        loadCenarioData('producao');

        // Atualizar contagens
        loadCenariosCounts();
      } else {
        alert(data.message || 'Erro ao enviar cenários para produção.');
      }
    })
    .catch((error) => {
      // Restaurar o botão
      button.disabled = false;
      button.innerHTML = originalText;

      alert(`Erro ao enviar cenários para produção: ${error.message}`);
    });
}

/**
 * Carrega dados de cenários usando o parâmetro load_all para carregar todos os registros
 * @param {string} status - Status dos cenários (novo, producao, inconsistente)
 * @param {Function} loadCallback - Função a ser chamada após o carregamento dos dados
 * @param {Function} renderCallback - Função a ser chamada após a renderização da tabela
 */
function loadCenarioDataWithLoadAll(status, loadCallback, renderCallback) {
  // Sincronizar com o valor mais recente da empresa selecionada
  const storedCompany = localStorage.getItem('selectedCompany');
  const headerCompanySelect = document.getElementById('company-select');

  // Priorizar o valor do seletor do header, depois localStorage, depois a variável global
  if (headerCompanySelect && headerCompanySelect.value) {
    window.cenariosDetalhes.selectedCompany = headerCompanySelect.value;
  } else if (storedCompany) {
    window.cenariosDetalhes.selectedCompany = storedCompany;
  } else if (window.selectedCompany) {
    window.cenariosDetalhes.selectedCompany = window.selectedCompany;
  }

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    console.warn('Nenhuma empresa selecionada para carregar cenários');
    if (loadCallback) loadCallback();
    if (renderCallback) renderCallback();
    return;
  }

  // Determinar o container com base no status
  let containerId;
  switch (status) {
    case 'producao':
      containerId = 'producao-table-container';
      break;
    case 'inconsistente':
      containerId = 'inconsistentes-table-container';
      break;
    case 'novo':
    default:
      containerId = 'novos-table-container';
      status = 'novo';
      break;
  }

  const container = document.getElementById(containerId);
  if (!container) {
    console.warn(`Container ${containerId} não encontrado`);
    if (loadCallback) loadCallback();
    if (renderCallback) renderCallback();
    return;
  }

  // Mostrar indicador de carregamento no container
  container.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
      <p class="mt-2">Carregando todos os cenários ${status}...</p>
    </div>
  `;

  // Parâmetros para a requisição
  const params = new URLSearchParams();
  const tipoTributo = window.cenariosDetalhes.currentTipoTributo;

  params.append('direcao', window.cenariosDetalhes.currentDirecao);
  params.append('status', status);
  params.append('load_all', 'true'); // Novo parâmetro para carregar todos os registros
  params.append('empresa_id', window.cenariosDetalhes.selectedCompany);

  // Adicionar filtros de ano e mês se estiverem definidos
  if (window.cenariosDetalhes.selectedYear) {
    params.append('year', window.cenariosDetalhes.selectedYear);
  }

  if (window.cenariosDetalhes.selectedMonth) {
    params.append('month', window.cenariosDetalhes.selectedMonth);
  }

  // Obter cenários da API
  fetch(`/api/cenarios/${tipoTributo}?${params.toString()}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.cenarios && data.cenarios.length > 0) {
        // Armazenar dados em cache
        window.cenariosDetalhes.cachedData[status] = data;

        // Limpar seleções anteriores para este status
        window.cenariosDetalhes.selectedCenarios[status] = [];

        // Se for status inconsistente, buscar os cenários em produção correspondentes
        if (status === 'inconsistente') {
          loadProducaoCenariosForInconsistentes(data.cenarios, () => {
            // Renderizar tabela de cenários após carregar os cenários em produção
            renderCenarioTable(
              container,
              data.cenarios,
              status,
              data.pagination,
            );
            if (loadCallback) loadCallback();
            // Aguardar um pouco para garantir que a renderização termine
            setTimeout(() => {
              if (renderCallback) renderCallback();
            }, 100);
          });
        } else {
          // Renderizar tabela de cenários
          renderCenarioTable(container, data.cenarios, status, data.pagination);
          if (loadCallback) loadCallback();
          // Aguardar um pouco para garantir que a renderização termine
          setTimeout(() => {
            if (renderCallback) renderCallback();
          }, 100);
        }
      } else {
        // Mostrar mensagem de nenhum cenário
        container.innerHTML = `
          <div class="alert alert-info">
            Nenhum cenário encontrado com status "${status}" para ${getTituloTributo()}.
          </div>
        `;
        if (loadCallback) loadCallback();
        if (renderCallback) renderCallback();
      }
    })
    .catch((error) => {
      console.error(`Erro ao carregar cenários ${status}:`, error);
      container.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar cenários: ${error.message}
        </div>
      `;
      if (loadCallback) loadCallback();
      if (renderCallback) renderCallback();
    });
}

/**
 * Mostra ou esconde o overlay de carregamento
 * @param {boolean} show - Se true, mostra o overlay; se false, esconde
 * @param {string} message - Mensagem a ser exibida no overlay
 */
function toggleLoadingOverlay(
  show,
  message = 'Carregando dados, por favor aguarde...',
) {
  // Verificar se o overlay já existe
  let overlay = document.getElementById('loading-overlay');

  if (show) {
    // Se o overlay não existe, criar um novo
    if (!overlay) {
      overlay = document.createElement('div');
      overlay.id = 'loading-overlay';
      overlay.className = 'loading-overlay';
      overlay.innerHTML = `
        <div class="loading-spinner">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
          <p>${message}</p>
        </div>
      `;
      document.body.appendChild(overlay);
    } else {
      // Atualizar a mensagem se o overlay já existe
      const messageElement = overlay.querySelector('p');
      if (messageElement) {
        messageElement.textContent = message;
      }
      overlay.style.display = 'flex';
    }

    // Atualizar o estado de carregamento
    window.cenariosDetalhes.isLoading = true;
  } else if (overlay) {
    // Esconder o overlay se ele existe
    overlay.style.display = 'none';

    // Atualizar o estado de carregamento
    window.cenariosDetalhes.isLoading = false;
  }
}

/**
 * Carrega os dados de cenários com suporte a paginação e cache
 * @param {string} status - Status dos cenários (novo, producao, inconsistente, conforme)
 * @param {number} page - Número da página a ser carregada (padrão: 1)
 * @param {number} perPage - Número de itens por página (padrão: 100)
 * @param {boolean} forceReload - Se true, força o recarregamento mesmo se houver dados em cache
 */
function loadCenarioData(
  status = 'novo',
  page = 1,
  perPage = 100,
  forceReload = false,
) {
  // Atualizar contagens de cenários quando carregar dados
  loadCenariosCounts();

  // Sincronizar com o valor mais recente da empresa selecionada
  const storedCompany = localStorage.getItem('selectedCompany');
  const headerCompanySelect = document.getElementById('company-select');

  // Priorizar o valor do seletor do header, depois localStorage, depois a variável global
  if (headerCompanySelect && headerCompanySelect.value) {
    window.cenariosDetalhes.selectedCompany = headerCompanySelect.value;
  } else if (storedCompany) {
    window.cenariosDetalhes.selectedCompany = storedCompany;
  } else if (window.selectedCompany) {
    window.cenariosDetalhes.selectedCompany = window.selectedCompany;
  }

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    // Mostrar mensagem de alerta em vez de interromper a função
  }

  // Normalizar o status para garantir consistência
  if (status === 'inconsistentes') {
    status = 'inconsistente';
  } else if (status === 'novos') {
    status = 'novo';
  }

  // Determinar o container com base no status
  let containerId;
  switch (status) {
    case 'producao':
      containerId = 'producao-table-container';
      break;
    case 'inconsistente':
      containerId = 'inconsistentes-table-container';
      break;
    case 'novo':
    default:
      containerId = 'novos-table-container';
      status = 'novo';
      break;
  }

  const container = document.getElementById(containerId);
  if (!container) {
    return;
  }

  // Verificar se temos dados em cache e não estamos forçando o recarregamento
  if (!forceReload && window.cenariosDetalhes.cachedData[status]) {
    console.log(`Usando dados em cache para status ${status}`);

    const cachedData = window.cenariosDetalhes.cachedData[status];

    // Se for status inconsistente, buscar os cenários em produção correspondentes
    if (status === 'inconsistente') {
      loadProducaoCenariosForInconsistentes(cachedData.cenarios, () => {
        // Renderizar tabela de cenários após carregar os cenários em produção
        renderCenarioTable(
          container,
          cachedData.cenarios,
          status,
          cachedData.pagination,
        );
      });
    } else {
      // Renderizar tabela de cenários
      renderCenarioTable(
        container,
        cachedData.cenarios,
        status,
        cachedData.pagination,
      );
    }

    return;
  }

  // Mostrar indicador de carregamento no container
  container.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
      <p class="mt-2">Carregando dados, por favor aguarde...</p>
    </div>
  `;

  // Mostrar overlay de carregamento se estamos carregando todos os cenários (sem paginação)
  if (perPage > 500) {
    toggleLoadingOverlay(
      true,
      'Carregando todos os cenários, isso pode levar alguns instantes...',
    );
  }

  // Parâmetros para a requisição
  const params = new URLSearchParams();

  // Usar o tipo de tributo atual
  const tipoTributo = window.cenariosDetalhes.currentTipoTributo;
  params.append('direcao', window.cenariosDetalhes.currentDirecao);
  params.append('status', status);

  // Usar o novo parâmetro load_all para carregar todos os cenários
  params.append('load_all', 'true');

  // Verificar se a empresa está selecionada e é válida
  if (window.cenariosDetalhes.selectedCompany) {
    const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);
    if (!isNaN(empresaId) && empresaId > 0) {
      params.append('empresa_id', empresaId);
    } else {
      // Mostrar mensagem de alerta no container
      container.innerHTML = `
        <div class="alert alert-warning">
          Por favor, selecione uma empresa válida para visualizar os cenários.
        </div>
      `;
      toggleLoadingOverlay(false);
      return;
    }
  } else {
    // Mostrar mensagem de alerta no container
    container.innerHTML = `
      <div class="alert alert-warning">
        Por favor, selecione uma empresa para visualizar os cenários.
      </div>
    `;
    toggleLoadingOverlay(false);
    return;
  }

  // Adicionar filtros de ano e mês se estiverem definidos
  if (window.cenariosDetalhes.selectedYear) {
    params.append('year', window.cenariosDetalhes.selectedYear);
  }

  if (window.cenariosDetalhes.selectedMonth) {
    params.append('month', window.cenariosDetalhes.selectedMonth);
  }

  // Obter cenários da API
  fetch(`/api/cenarios/${tipoTributo}?${params.toString()}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder overlay de carregamento
      toggleLoadingOverlay(false);

      if (data.cenarios && data.cenarios.length > 0) {
        // Armazenar dados em cache
        window.cenariosDetalhes.cachedData[status] = data;

        // Limpar seleções anteriores para este status
        window.cenariosDetalhes.selectedCenarios[status] = [];

        // Se for status inconsistente, buscar os cenários em produção correspondentes
        if (status === 'inconsistente') {
          loadProducaoCenariosForInconsistentes(data.cenarios, () => {
            // Renderizar tabela de cenários após carregar os cenários em produção
            renderCenarioTable(
              container,
              data.cenarios,
              status,
              data.pagination,
            );
          });
        } else {
          // Renderizar tabela de cenários
          renderCenarioTable(container, data.cenarios, status, data.pagination);
        }
      } else {
        // Mostrar mensagem de nenhum cenário
        container.innerHTML = `
          <div class="alert alert-info">
            Nenhum cenário encontrado com status "${status}" para ${getTituloTributo()}.
          </div>
        `;
      }
    })
    .catch((error) => {
      // Esconder overlay de carregamento
      toggleLoadingOverlay(false);

      container.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar cenários: ${error.message}
        </div>
      `;
    });
}

/**
 * Carrega os cenários em produção correspondentes aos cenários inconsistentes
 * @param {Array} cenarios - Lista de cenários inconsistentes
 * @param {Function} callback - Função a ser chamada após carregar os cenários em produção
 */
function loadProducaoCenariosForInconsistentes(cenarios, callback) {
  // Inicializar o objeto para armazenar os cenários em produção
  window.cenariosDetalhes.producaoCenarios =
    window.cenariosDetalhes.producaoCenarios || {};

  // Criar um contador para controlar quando todos os cenários foram carregados
  let pendingRequests = cenarios.length;

  // Se não houver cenários, chamar o callback imediatamente
  if (pendingRequests === 0) {
    if (callback) callback();
    return;
  }

  // Para cada cenário inconsistente, buscar o cenário em produção correspondente
  cenarios.forEach((cenario) => {
    const params = new URLSearchParams();
    params.append('status', 'producao');
    params.append('produto_id', cenario.produto_id);
    params.append('cliente_id', cenario.cliente_id);

    fetch(
      `/api/cenarios/${
        window.cenariosDetalhes.currentTipoTributo
      }?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      },
    )
      .then((response) => response.json())
      .then((data) => {
        if (data.cenarios && data.cenarios.length > 0) {
          // Armazenar o cenário em produção
          window.cenariosDetalhes.producaoCenarios[cenario.id] =
            data.cenarios[0];
        } else {
        }

        // Decrementar o contador de requisições pendentes
        pendingRequests--;

        // Se todas as requisições foram concluídas, chamar o callback
        if (pendingRequests === 0 && callback) {
          callback();
        }
      })
      .catch((error) => {
        // Decrementar o contador de requisições pendentes mesmo em caso de erro
        pendingRequests--;

        // Se todas as requisições foram concluídas, chamar o callback
        if (pendingRequests === 0 && callback) {
          callback();
        }
      });
  });
}

/**
 * Cria os filtros individuais para cada coluna
 * @param {string} status - Status dos cenários
 * @returns {string} HTML com os filtros individuais
 */
function createColumnFilters(status) {
  // Obter colunas para o tipo de tributo atual
  const columns = getColumnsForTributoType(status);

  // Remover a coluna de ações
  const columnsWithoutActions = columns.filter((col) => col.name !== 'Ações');

  // Criar filtros apenas para colunas visíveis
  let visibleColumnIndex = 0;
  return (
    columnsWithoutActions
      .map((column, originalIndex) => {
        if (column.visible) {
          const filterHtml = `<th data-column-index="${originalIndex}" data-visible-index="${visibleColumnIndex}">
            <input type="text" class="form-control form-control-sm column-filter"
              data-column="${visibleColumnIndex}" data-column-name="${column.name}" data-original-index="${originalIndex}"
              placeholder="Filtrar ${column.name}..."
              style="width: 100%; font-size: 0.8rem;">
          </th>`;
          visibleColumnIndex++;
          return filterHtml;
        } else {
          // Para colunas ocultas, criar um th vazio e oculto
          return `<th class="d-none" data-column-index="${originalIndex}"></th>`;
        }
      })
      .join('') + '<th></th>'
  ); // Coluna vazia para ações
}

/**
 * Renderiza a tabela de cenários com suporte a paginação
 * @param {HTMLElement} container - Container onde a tabela será renderizada
 * @param {Array} cenarios - Lista de cenários
 * @param {string} status - Status dos cenários
 * @param {Object} pagination - Informações de paginação
 */
function renderCenarioTable(container, cenarios, status, pagination) {
  // Criar a tabela com colunas específicas para cada tipo de tributo
  const tableHeaders = getCenarioTableHeaders(status);
  const tableRows = getCenarioTableRows(cenarios, status);
  const columnFilters = createColumnFilters(status);

  // Criar informações de total de registros (sem paginação)
  let totalInfo = '';
  if (pagination && pagination.total) {
    totalInfo = `
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
          <span class="text-muted me-3">Total: ${pagination.total} registros carregados</span>
        </div>
      </div>
    `;
  }

  // Criar botões de ação em lote apenas para a aba "Novos"
  let batchActionsHtml = '';
  if (status === 'novo') {
    batchActionsHtml = `
      <div class="batch-actions-container">
        <div class="selection-info">
          <span id="selection-count-${status}">0</span> cenários selecionados
        </div>
        <div>
          <button id="select-all-btn-${status}" class="btn btn-outline-primary">
            <i class="fas fa-check-square me-1"></i>Selecionar Todos
          </button>
          <button id="clear-selection-btn-${status}" class="btn btn-outline-secondary">
            <i class="fas fa-times me-1"></i>Limpar Seleção
          </button>
          <button id="batch-to-production-btn-${status}" class="btn btn-success" disabled>
            <i class="fas fa-check-circle me-1"></i>Enviar para Produção
          </button>
          <button id="batch-to-inconsistent-btn-${status}" class="btn btn-warning" disabled>
            <i class="fas fa-exclamation-circle me-1"></i>Marcar como Inconsistente
          </button>
        </div>
      </div>
    `;
  }

  // Adicionar controles de filtro, seleção de colunas, botões de ação em lote e informações de total
  container.innerHTML = `
    <div class="mb-3">
      <div class="row">
        <div class="col-12">
          <div class="input-group">
            <input type="text" class="form-control" id="cenario-filter-${status}" placeholder="Filtro geral...">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
              Colunas <i class="fas fa-columns"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end column-selector" id="column-selector-${status}">
              ${getColumnSelectorOptions(status)}
            </ul>
          </div>
        </div>
      </div>
    </div>

    ${batchActionsHtml}

    ${totalInfo}

    <div class="table-container-with-dual-scroll">
      <!-- Scrollbar superior -->
      <div class="table-top-scrollbar" id="table-top-scrollbar-${status}">
        <div class="table-top-scrollbar-content" id="table-top-scrollbar-content-${status}"></div>
      </div>

      <!-- Tabela principal -->
      <div class="table-responsive" id="table-responsive-${status}">
        <table class="table table-striped table-hover" id="cenario-${status}-table">
          <thead>
            <tr>
              ${
                status === 'novo'
                  ? `
              <th>
                <input type="checkbox" class="select-all-checkbox" id="select-all-checkbox-${status}">
              </th>
              `
                  : ''
              }
              ${tableHeaders}
              <th>Ações</th>
            </tr>
            <tr class="filters">
              ${status === 'novo' ? '<th></th>' : ''}
              ${columnFilters}
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      </div>
    </div>
  `;

  // Inicializar DataTable com as novas configurações
  initializeDataTable(status);

  // Configurar eventos para os botões de ação em lote apenas para a aba "Novos"
  if (status === 'novo') {
    setupBatchActionButtons(status, cenarios);
  }
}

/**
 * Gera os links de paginação
 * @param {number} currentPage - Página atual
 * @param {number} lastPage - Última página
 * @returns {string} HTML com os links de paginação
 */
function getPaginationLinks(currentPage, lastPage) {
  let links = '';
  const maxLinks = 5; // Número máximo de links a serem exibidos

  // Determinar o intervalo de páginas a serem exibidas
  let startPage = Math.max(1, currentPage - Math.floor(maxLinks / 2));
  let endPage = Math.min(lastPage, startPage + maxLinks - 1);

  // Ajustar o intervalo se necessário
  if (endPage - startPage + 1 < maxLinks && startPage > 1) {
    startPage = Math.max(1, endPage - maxLinks + 1);
  }

  // Gerar os links
  for (let i = startPage; i <= endPage; i++) {
    links += `
      <li class="page-item ${i === currentPage ? 'active' : ''}">
        <a class="page-link" href="#" data-page="${i}">${i}</a>
      </li>
    `;
  }

  return links;
}

/**
 * Configura eventos para os links de paginação
 * @param {HTMLElement} container - Container onde a tabela está renderizada
 * @param {string} status - Status dos cenários
 */
function setupPaginationEvents(container, status) {
  // Adicionar evento de clique para os links de paginação
  const paginationLinks = container.querySelectorAll('.pagination .page-link');
  console.log(
    `Configurando ${paginationLinks.length} links de paginação para status ${status}`,
  );

  // Remover event listeners anteriores para evitar duplicação
  paginationLinks.forEach((link) => {
    const newLink = link.cloneNode(true);
    link.parentNode.replaceChild(newLink, link);

    newLink.addEventListener('click', function (event) {
      event.preventDefault();
      const page = parseInt(this.getAttribute('data-page'));
      if (!isNaN(page)) {
        console.log(`Navegando para a página ${page} com status ${status}`);
        // Carregar a página selecionada
        const perPage =
          document.getElementById(`per-page-select-${status}`)?.value || 100;
        loadCenarioData(status, page, perPage);
      }
    });
  });
}

/**
 * Configura o seletor de itens por página
 * @param {HTMLElement} container - Container onde a tabela está renderizada
 * @param {string} status - Status dos cenários
 */
function setupPerPageSelect(container, status) {
  const perPageSelect = document.getElementById(`per-page-select-${status}`);
  if (perPageSelect) {
    console.log(
      `Configurando seletor de itens por página para status ${status}`,
    );

    // Remover event listeners anteriores para evitar duplicação
    const newSelect = perPageSelect.cloneNode(true);
    perPageSelect.parentNode.replaceChild(newSelect, perPageSelect);

    // Adicionar novo event listener
    newSelect.addEventListener('change', function () {
      console.log(
        `Alterando para ${this.value} itens por página com status ${status}`,
      );
      // Ao mudar a quantidade de itens por página, voltar para a primeira página
      loadCenarioData(status, 1, this.value);
    });
  } else {
    console.warn(`Elemento per-page-select-${status} não encontrado`);
  }
}

/**
 * Inicializa o DataTable para a tabela de cenários
 * @param {string} status - Status dos cenários
 */
function initializeDataTable(status) {
  try {
    // Destruir DataTable existente se houver
    const existingTable = $(`#cenario-${status}-table`).DataTable.isDataTable(
      `#cenario-${status}-table`,
    );
    if (existingTable) {
      console.log(`Destruindo DataTable existente para status ${status}`);

      // Limpar todos os event listeners dos filtros antes de destruir
      const tableContainer = document
        .querySelector(`#cenario-${status}-table`)
        ?.closest('.table-responsive');
      if (tableContainer) {
        const filterInputs = tableContainer.querySelectorAll(
          'input.column-filter',
        );
        filterInputs.forEach((input) => {
          $(input).off(); // Remove todos os event listeners
        });
      }

      $(`#cenario-${status}-table`).DataTable().destroy();

      // Limpar configurações de scrollbars duplas se existirem
      cleanupDualScrollbars(status);

      // Limpar referência específica da tabela
      if (window.cenariosDetalhes.cenarioTables[status]) {
        window.cenariosDetalhes.cenarioTables[status] = null;
      }
    }

    // Obter as colunas para definir a visibilidade inicial
    const columns = getColumnsForTributoType(status);
    const columnsWithoutActions = columns.filter((col) => col.name !== 'Ações');

    // Configurar a visibilidade inicial das colunas
    const columnDefs = columnsWithoutActions.map((column, index) => {
      // Garantir que a coluna NCM sempre esteja visível
      const isVisible = column.name === 'NCM' ? true : column.visible;
      // Ajustar o target considerando a coluna de checkbox na aba "Novos"
      const targetIndex = status === 'novo' ? index + 1 : index;
      return {
        targets: targetIndex,
        visible: isVisible,
        className: isVisible ? '' : 'd-none',
      };
    });

    const dataTableOptions = {
      language: {
        url: '/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      // Ordenar por NCM (pode ser ajustado conforme necessário)
      // Ajustar o índice de ordenação dependendo da presença da coluna de checkbox
      order: [[status === 'novo' ? 2 : 1, 'desc']],
      columnDefs: columnDefs, // Definir visibilidade inicial das colunas
      // Desativar a paginação do DataTables, pois estamos usando nossa própria paginação no servidor
      paging: false,
      // Mostrar todas as linhas na tabela
      lengthChange: true,
      info: false, // Não mostrar informações de paginação do DataTables
      // Manter a pesquisa ativa para os filtros individuais funcionarem, mas ocultar o campo de busca
      searching: true,
      // Ocultar o campo de busca padrão
      dom: 'lrtip', // Remove 'f' (filter/search) do layout padrão
      // Configurações de pesquisa
      search: {
        smart: true, // Desativar pesquisa "smart" para permitir correspondências parciais
        regex: true,
        caseInsensitive: true,
      },
      // Melhorar a performance
      deferRender: true,
      // Configurar processamento de strings para pesquisa
      searchDelay: 300,
      // Configurar o processamento de dados para garantir que a pesquisa funcione corretamente
      processing: true,
      initComplete: function () {
        // Aplicar filtros individuais para cada coluna
        const api = this.api();

        // Os filtros individuais serão configurados posteriormente na função setupColumnFilters
        // para evitar conflitos com a inicialização do DataTable

        // Garantir que as colunas estejam corretamente visíveis/ocultas na inicialização
        columnsWithoutActions.forEach((column, index) => {
          // Definir a visibilidade inicial da coluna
          // Adicionar +1 ao índice apenas para a aba "Novos" para considerar a coluna de checkbox
          const adjustedIndex = status === 'novo' ? index + 1 : index;
          // Usar a configuração de visibilidade definida na coluna, forçando NCM sempre visível
          const shouldBeVisible = column.name === 'NCM' ? true : column.visible;

          // Forçar a aplicação da visibilidade
          api.column(adjustedIndex).visible(shouldBeVisible, false);

          // Atualizar a visibilidade do filtro correspondente
          $(`.filters th[data-column-index="${index}"]`).toggleClass(
            'd-none',
            !shouldBeVisible,
          );

          // Atualizar a visibilidade das células da coluna
          $(`td[data-column-index="${index}"]`).toggleClass(
            'd-none',
            !shouldBeVisible,
          );
        });

        // Forçar redesenho completo para garantir que as colunas ocultas sejam realmente ocultadas
        api.columns.adjust().draw(true);

        // Aplicar novamente a visibilidade após o redesenho para garantir que funcione
        setTimeout(() => {
          columnsWithoutActions.forEach((column, index) => {
            const adjustedIndex = status === 'novo' ? index + 1 : index;
            const shouldBeVisible =
              column.name === 'NCM' ? true : column.visible;

            // Aplicar a visibilidade correta para todas as colunas
            api.column(adjustedIndex).visible(shouldBeVisible, false);
          });
          api.columns.adjust().draw(false);
        }, 100);
      },
      drawCallback: function () {
        // Garantir que as colunas estejam corretamente visíveis/ocultas após cada redesenho
        const api = this.api();
        columnsWithoutActions.forEach((column, index) => {
          // Adicionar +1 ao índice apenas para a aba "Novos" para considerar a coluna de checkbox
          const adjustedIndex = status === 'novo' ? index + 1 : index;
          const isVisible = api.column(adjustedIndex).visible();

          // Atualizar a visibilidade do filtro correspondente baseado na visibilidade da coluna
          const $filterTh = $(`.filters th[data-column-index="${index}"]`);
          if (column.visible && isVisible) {
            $filterTh.removeClass('d-none');
          } else {
            $filterTh.addClass('d-none');
          }

          // Atualizar a visibilidade das células da coluna
          $(`td[data-column-index="${index}"]`).toggleClass(
            'd-none',
            !isVisible,
          );

          // Garantir que colunas importantes sempre estejam visíveis conforme configuração
          if (column.name === 'NCM' || (column.visible && !isVisible)) {
            api.column(adjustedIndex).visible(column.visible, false);
            if (column.visible) {
              $filterTh.removeClass('d-none');
              $(`td[data-column-index="${index}"]`).removeClass('d-none');
            }
          }
        });
      },
    };

    // Criar DataTable e armazenar referência específica por status
    window.cenariosDetalhes.cenarioTables[status] = $(
      `#cenario-${status}-table`,
    ).DataTable(dataTableOptions);

    // Atualizar currentStatus para manter compatibilidade
    window.cenariosDetalhes.currentStatus = status;

    // Configurar filtro geral personalizado (já que desabilitamos o searching do DataTables)
    $(`#cenario-filter-${status}`).on('keyup', function () {
      const searchValue = this.value.toLowerCase();
      const table = window.cenariosDetalhes.cenarioTable;

      // Aplicar filtro personalizado
      $.fn.dataTable.ext.search.push(function (settings, data, dataIndex) {
        // Verificar se é a tabela correta
        if (settings.nTable.id !== `cenario-${status}-table`) {
          return true;
        }

        // Se não há valor de pesquisa, mostrar todas as linhas
        if (!searchValue) {
          return true;
        }

        // Pesquisar em todas as colunas visíveis
        for (let i = 0; i < data.length; i++) {
          if (data[i].toLowerCase().indexOf(searchValue) !== -1) {
            return true;
          }
        }
        return false;
      });

      table.draw();

      // Remover o filtro após aplicar para não afetar outras tabelas
      $.fn.dataTable.ext.search.pop();
    });

    // Configurar seletor de colunas
    setupColumnSelector(status);
  } catch (error) {
    console.error('Erro ao inicializar DataTable:', error);
  }

  // Configurar eventos para os botões
  setupCenarioButtons();

  // Configurar os checkboxes de seleção
  setTimeout(() => {
    setupSelectionCheckboxes();
    console.log(
      'Checkboxes de seleção configurados após inicialização do DataTable',
    );
  }, 100);

  // Configurar filtros individuais após a inicialização completa
  setTimeout(() => {
    setupColumnFilters(status);
  }, 500);

  // Configurar scrollbars duplas após a inicialização completa
  setTimeout(() => {
    setupDualScrollbars(status);
  }, 600);
}

/**
 * Configura os filtros individuais das colunas
 * @param {string} status - Status dos cenários
 */
function setupColumnFilters(status) {
  const table = window.cenariosDetalhes.cenarioTables[status];
  if (!table) {
    console.warn(
      `DataTable não encontrado para configurar filtros do status ${status}`,
    );
    return;
  }

  // Aguardar um pouco mais para garantir que tudo esteja renderizado
  setTimeout(() => {
    // Selecionar todos os inputs de filtro da tabela específica
    const tableContainer = document
      .querySelector(`#cenario-${status}-table`)
      ?.closest('.table-responsive');

    if (!tableContainer) {
      console.warn(`Container da tabela não encontrado para status ${status}`);
      return;
    }

    const filterInputs = tableContainer.querySelectorAll('input.column-filter');

    console.log(
      `Configurando ${filterInputs.length} filtros para status ${status}`,
    );

    // Verificar se a tabela ainda é a mesma (evitar conflitos após recarregamento)
    const currentTable = window.cenariosDetalhes.cenarioTables[status];
    if (!currentTable || table !== currentTable) {
      console.warn(
        `Tabela mudou durante configuração dos filtros para status ${status}`,
      );
      return;
    }

    filterInputs.forEach((input) => {
      const visibleColumnIndex = parseInt(input.getAttribute('data-column'));
      const $input = $(input);

      // Remover todos os eventos anteriores
      $input.off();

      // Verificar se o input ainda existe e está visível
      if (!input.isConnected || !$(input).is(':visible')) {
        return;
      }

      // Configurar eventos de forma mais robusta
      let filterTimeout;

      // Evento principal de filtro
      $input.on('input', function (e) {
        e.stopPropagation();
        e.stopImmediatePropagation();

        const inputValue = this.value;

        // Limpar timeout anterior
        clearTimeout(filterTimeout);

        // Aplicar filtro com delay menor para melhor responsividade
        filterTimeout = setTimeout(() => {
          try {
            // Verificar se a tabela ainda existe e é a mesma
            const currentTable = window.cenariosDetalhes.cenarioTables[status];
            if (!currentTable || currentTable !== table) {
              console.warn(
                `Tabela mudou durante aplicação do filtro para status ${status}`,
              );
              return;
            }

            // Usar o índice original da coluna, não o visível
            const originalIndex = parseInt(
              input.getAttribute('data-original-index'),
            );
            const adjustedIndex =
              status === 'novo' ? originalIndex + 1 : originalIndex;

            // Verificar se a coluna existe e está visível
            const column = currentTable.column(adjustedIndex);
            if (column && column.visible()) {
              column.search(inputValue).draw();
            }
          } catch (error) {
            console.warn('Erro ao aplicar filtro:', error, {
              originalIndex: input.getAttribute('data-original-index'),
              visibleIndex: visibleColumnIndex,
              inputValue: inputValue,
              status: status,
            });
          }
        }, 100);
      });

      // Evento separado para keyup para casos especiais
      $input.on('keyup', function (e) {
        e.stopPropagation();
        e.stopImmediatePropagation();

        // Para teclas especiais como Delete, Backspace, etc.
        if (
          [
            'Delete',
            'Backspace',
            'ArrowLeft',
            'ArrowRight',
            'Home',
            'End',
          ].includes(e.key)
        ) {
          const inputValue = this.value;
          clearTimeout(filterTimeout);

          filterTimeout = setTimeout(() => {
            try {
              // Verificar se a tabela ainda existe e é a mesma
              const currentTable =
                window.cenariosDetalhes.cenarioTables[status];
              if (!currentTable || currentTable !== table) {
                console.warn(
                  `Tabela mudou durante aplicação do filtro (keyup) para status ${status}`,
                );
                return;
              }

              // Usar o índice original da coluna, não o visível
              const originalIndex = parseInt(
                input.getAttribute('data-original-index'),
              );
              const adjustedIndex =
                status === 'novo' ? originalIndex + 1 : originalIndex;

              // Verificar se a coluna existe e está visível
              const column = currentTable.column(adjustedIndex);
              if (column && column.visible()) {
                column.search(inputValue).draw();
              }
            } catch (error) {
              console.warn('Erro ao aplicar filtro (keyup):', error, {
                status: status,
                originalIndex: input.getAttribute('data-original-index'),
              });
            }
          }, 50); // Delay ainda menor para teclas de navegação
        }
      });

      // Prevenir interferência do DataTable e melhorar a experiência do usuário
      $input.on('focus click', function (e) {
        e.stopPropagation();
        e.stopImmediatePropagation();

        // Limpar qualquer timeout de restauração de foco de outros filtros
        // para evitar conflitos quando o usuário clica em um novo filtro
        clearTimeout(window.cenariosDetalhes.focusRestoreTimeout);
      });

      // Prevenir que o Enter submeta formulários
      $input.on('keydown', function (e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          e.stopPropagation();
        }
      });

      // Manter o foco durante a digitação apenas quando necessário
      $input.on('blur', function (e) {
        // Se o blur foi causado pelo redesenho da tabela, restaurar o foco
        const currentTarget = e.currentTarget;

        // Usar uma variável global para controlar o timeout e evitar conflitos
        window.cenariosDetalhes.focusRestoreTimeout = setTimeout(() => {
          // Verificar se o elemento que recebeu o foco é outro filtro ou elemento interativo
          const activeElement = document.activeElement;
          const isAnotherFilter =
            activeElement && activeElement.classList.contains('column-filter');
          const isInteractiveElement =
            activeElement &&
            (activeElement.tagName === 'BUTTON' ||
              activeElement.tagName === 'INPUT' ||
              activeElement.tagName === 'SELECT' ||
              activeElement.tagName === 'TEXTAREA' ||
              activeElement.classList.contains('btn') ||
              activeElement.classList.contains('form-control'));

          // Só restaurar o foco se:
          // 1. O elemento atual não está mais focado
          // 2. Tem texto digitado
          // 3. O novo elemento focado NÃO é outro filtro ou elemento interativo
          // 4. O novo elemento focado não é um descendente de um dropdown ou modal
          const isInDropdown =
            activeElement &&
            activeElement.closest('.dropdown-menu, .modal, .popover');

          if (
            activeElement !== currentTarget &&
            currentTarget.value.length > 0 &&
            !isAnotherFilter &&
            !isInteractiveElement &&
            !isInDropdown
          ) {
            // Só restaurar o foco se o usuário estava digitando e não está interagindo com outros elementos
            const selection = currentTarget.selectionStart;
            currentTarget.focus();
            currentTarget.setSelectionRange(selection, selection);
          }
        }, 10);
      });

      // Garantir que o campo seja focalizável
      input.setAttribute('tabindex', '0');

      // Adicionar estilo para melhor UX
      input.style.outline = 'none';
      input.style.borderColor = '#ced4da';
    });

    console.log(`Filtros configurados com sucesso para status ${status}`);
  }, 100);
}

/**
 * Configura o seletor de colunas
 * @param {string} status - Status dos cenários
 */
function setupColumnSelector(status) {
  const table = window.cenariosDetalhes.cenarioTables[status];

  if (!table) {
    console.warn(
      `DataTable não encontrado para configurar seletor de colunas do status ${status}`,
    );
    return;
  }

  // Adicionar event listeners para os checkboxes de colunas
  $(`#column-selector-${status} input[type="checkbox"]`).on(
    'change',
    function () {
      const columnIndex = $(this).data('column');
      const columnName = $(`label[for="column-${columnIndex}"]`).text().trim();

      // Obter as colunas definidas no getColumnsForTributoType
      const definedColumns = getColumnsForTributoType(status);
      const columnsWithoutActions = definedColumns.filter(
        (col) => col.name !== 'Ações',
      );

      // Verificar se a coluna deve ser forçada a ficar visível
      const column = columnsWithoutActions.find(
        (col) => col.name === columnName,
      );
      const isForceVisible = column?.forceVisible || columnName === 'NCM';

      // Se a coluna tem forceVisible=true ou é NCM, não permitir desmarcar o checkbox
      if (isForceVisible && !$(this).prop('checked')) {
        $(this).prop('checked', true);
        return;
      }

      // Adicionar +1 ao índice apenas para a aba "Novos" para considerar a coluna de checkbox
      const adjustedIndex = status === 'novo' ? columnIndex + 1 : columnIndex;
      const tableColumn = table.column(adjustedIndex);
      const isVisible = $(this).prop('checked'); // Usar o estado atual do checkbox

      // Alternar visibilidade da coluna
      tableColumn.visible(isVisible, false); // false para não redesenhar imediatamente

      // Também atualizar a visibilidade do filtro correspondente
      $(`.filters th[data-column-index="${columnIndex}"]`).toggleClass(
        'd-none',
        !isVisible,
      );

      // Atualizar a visibilidade das células da coluna
      $(`td[data-column-index="${columnIndex}"]`).toggleClass(
        'd-none',
        !isVisible,
      );

      // Forçar a atualização da coluna
      if (isVisible) {
        // Se estamos tornando a coluna visível, forçar a renderização
        // Primeiro, obter os dados da coluna
        const columnData = [];
        table
          .column(adjustedIndex)
          .nodes()
          .each(function () {
            columnData.push($(this).html());
          });

        // Forçar a renderização
        column.visible(false, false).visible(true, false);

        // Garantir que os dados estejam corretos
        table
          .column(adjustedIndex)
          .nodes()
          .each(function (i) {
            if (i < columnData.length && !$(this).html()) {
              $(this).html(columnData[i]);
            }
          });
      }

      // Redesenhar a tabela para aplicar as alterações
      table.columns.adjust().draw(false);
    },
  );

  // Adicionar event listeners para os botões de selecionar/deselecionar todos
  $(`#column-selector-${status} .select-all-columns`).on('click', function () {
    // Primeiro, marcar todos os checkboxes
    $(`#column-selector-${status} input[type="checkbox"]`).prop(
      'checked',
      true,
    );

    // Depois, atualizar a visibilidade de todas as colunas de uma vez
    table.columns().every(function (index) {
      // Para a aba "Novos", pular a coluna de checkbox (índice 0)
      // Para as outras abas, não há coluna de checkbox para pular
      const shouldProcess = status === 'novo' ? index > 0 : true;
      if (shouldProcess) {
        this.visible(true, false);
        // Ajustar o índice para o data-column-index
        const dataIndex = status === 'novo' ? index - 1 : index;
        $(`.filters th[data-column-index="${dataIndex}"]`).removeClass(
          'd-none',
        );
        $(`td[data-column-index="${dataIndex}"]`).removeClass('d-none');
      }
    });

    // Redesenhar a tabela para aplicar as alterações
    table.columns.adjust().draw(false);
  });

  $(`#column-selector-${status} .deselect-all-columns`).on(
    'click',
    function () {
      // Obter as colunas definidas no getColumnsForTributoType
      const definedColumns = getColumnsForTributoType(status);
      const columnsWithoutActions = definedColumns.filter(
        (col) => col.name !== 'Ações',
      );

      // Mapear os nomes das colunas para seus atributos
      const columnAttributesMap = {};
      columnsWithoutActions.forEach((col) => {
        columnAttributesMap[col.name] = {
          visible: col.visible,
          forceVisible: col.forceVisible || false,
        };
      });

      // Primeiro, desmarcar todos os checkboxes, exceto os das colunas que devem permanecer visíveis
      $(`#column-selector-${status} input[type="checkbox"]`).each(function () {
        const columnIndex = $(this).data('column');
        const columnName = $(`label[for="column-${columnIndex}"]`)
          .text()
          .trim();

        // Manter visíveis as colunas essenciais e as que têm forceVisible=true
        const essentialColumns = [
          'Produto',
          'Descrição',
          'NCM',
          'Estado',
          'Cliente',
          'Ações',
        ];

        // Verificar se a coluna deve ser forçada a ficar visível
        const isForceVisible =
          columnAttributesMap[columnName]?.forceVisible || false;
        const keepVisible =
          essentialColumns.includes(columnName) || isForceVisible;

        $(this).prop('checked', keepVisible);

        // Atualizar a visibilidade da coluna
        // Adicionar +1 ao índice apenas para a aba "Novos" para considerar a coluna de checkbox
        const adjustedIndex = status === 'novo' ? columnIndex + 1 : columnIndex;
        const column = table.column(adjustedIndex);
        column.visible(keepVisible, false);

        // Atualizar a visibilidade do filtro e das células
        $(`.filters th[data-column-index="${columnIndex}"]`).toggleClass(
          'd-none',
          !keepVisible,
        );
        $(`td[data-column-index="${columnIndex}"]`).toggleClass(
          'd-none',
          !keepVisible,
        );
      });

      // Redesenhar a tabela para aplicar as alterações
      table.columns.adjust().draw(false);
    },
  );

  // Garantir que o estado inicial dos checkboxes corresponda à visibilidade das colunas
  // e que as colunas estejam corretamente visíveis/ocultas
  setTimeout(() => {
    // Primeiro, obter as colunas definidas no getColumnsForTributoType
    const definedColumns = getColumnsForTributoType(status);
    const columnsWithoutActions = definedColumns.filter(
      (col) => col.name !== 'Ações',
    );

    // Mapear os nomes das colunas para seus estados de visibilidade definidos
    const columnVisibilityMap = {};
    columnsWithoutActions.forEach((col) => {
      columnVisibilityMap[col.name] = col.visible;
    });

    // Atualizar a visibilidade de cada coluna
    table.columns().every(function (index) {
      const $checkbox = $(
        `#column-selector-${status} input[data-column="${index}"]`,
      );

      if ($checkbox.length > 0) {
        const columnName = $(`label[for="column-${index}"]`).text().trim();

        // Verificar se a coluna deve ser forçada a ficar visível
        const column = columnsWithoutActions.find(
          (col) => col.name === columnName,
        );
        const isForceVisible = column?.forceVisible || false;

        // Se a coluna tem forceVisible=true, ela deve estar sempre visível
        const shouldBeVisible = isForceVisible
          ? true
          : columnVisibilityMap[columnName] !== undefined
          ? columnVisibilityMap[columnName]
          : this.visible();

        // Atualizar o estado do checkbox
        $checkbox.prop('checked', shouldBeVisible);

        // Atualizar a visibilidade da coluna
        // Adicionar +1 ao índice para considerar a coluna de checkbox
        this.visible(shouldBeVisible, false);

        // Log para debug
        if (columnName === 'Destinação') {
          console.log(
            `Configurando coluna Destinação: shouldBeVisible=${shouldBeVisible}, checkbox checked=${$checkbox.prop(
              'checked',
            )}`,
          );
        }

        // Atualizar a visibilidade do filtro correspondente
        $(`.filters th[data-column-index="${index}"]`).toggleClass(
          'd-none',
          !shouldBeVisible,
        );

        // Atualizar a visibilidade das células da coluna
        $(`td[data-column-index="${index}"]`).toggleClass(
          'd-none',
          !shouldBeVisible,
        );
      }
    });

    // Redesenhar a tabela para aplicar as alterações
    table.columns.adjust().draw(false);
  }, 100);
}

/**
 * Obtém as opções para o seletor de colunas
 * @param {string} status - Status dos cenários (novo, producao, inconsistente)
 * @returns {string} HTML com as opções de colunas
 */
function getColumnSelectorOptions(status) {
  // Obter colunas específicas para o tipo de tributo atual
  const columns = getColumnsForTributoType(status);

  return (
    columns
      .map((column, index) => {
        return `
      <li>
        <div class="form-check ms-2 me-2">
          <input class="form-check-input" type="checkbox" id="column-${index}" data-column="${index}" ${
          column.visible ? 'checked' : ''
        }>
          <label class="form-check-label" for="column-${index}">
            ${column.name}
          </label>
        </div>
      </li>
    `;
      })
      .join('') +
    `
    <li><hr class="dropdown-divider"></li>
    <li>
      <div class="d-flex justify-content-between px-3">
        <button class="btn btn-sm btn-outline-primary select-all-columns">Selecionar Todos</button>
        <button class="btn btn-sm btn-outline-secondary deselect-all-columns">Limpar</button>
      </div>
    </li>
  `
  );
}

/**
 * Obtém as colunas específicas para o tipo de tributo atual
 * @param {string} status - Status dos cenários (novo, producao, inconsistente)
 * @returns {Array} Lista de nomes de colunas e suas configurações
 */
function getColumnsForTributoType(status = 'novo') {
  // Determinar se as colunas de Ativo e Vigência devem ser visíveis
  const showAtivoVigencia = status === 'producao';

  // Determinar se EX e CEST devem ser visíveis com base no tipo de tributo
  // CEST deve ser visível para ICMS, ICMS-ST e IPI
  // EX deve ser visível apenas para IPI
  const showCest = ['icms', 'icms_st', 'ipi'].includes(
    window.cenariosDetalhes.currentTipoTributo,
  );
  const showEx = window.cenariosDetalhes.currentTipoTributo === 'ipi';

  // Colunas comuns para todos os tipos de tributo com a nova ordem
  // Garantir que a coluna NCM esteja sempre visível
  const commonColumns = [
    { name: 'Produto', visible: true },
    { name: 'Descrição', visible: true },
    { name: 'NCM', visible: true, forceVisible: true }, // Coluna NCM sempre visível e forçada
    { name: 'EX', visible: showEx },
    { name: 'CEST', visible: showCest },
    { name: 'CFOP', visible: true },
  ];

  // Colunas específicas para cada tipo de tributo
  let specificColumns = [];
  let tributeColumns = [];

  // Colunas de cliente comuns para todos os tipos
  const clientColumns = [
    { name: 'Estado', visible: true },
    { name: 'Cliente', visible: true },
    { name: 'Simples Nacional', visible: true },
    { name: 'Atividade', visible: true },
    { name: 'Destinação', visible: true },
  ];

  // Colunas de status comuns para todos os tipos
  const statusColumns = [
    { name: 'Ativo', visible: showAtivoVigencia },
    { name: 'Vigência', visible: showAtivoVigencia },
  ];

  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      tributeColumns = [
        { name: 'Origem', visible: true },
        { name: 'CST', visible: true },
        { name: '% ICMS', visible: true },
        { name: '% Red. BC ICMS', visible: true },
        { name: '% ICMS Diferido', visible: true },
      ];
      break;
    case 'icms_st':
      tributeColumns = [
        { name: 'Origem', visible: true },
        { name: 'CST', visible: true },
        { name: '% ICMS', visible: true },
        { name: '% Red. BC ICMS', visible: true },
        { name: '% MVA', visible: true },
        { name: '% ICMS ST', visible: true },
        { name: '% Red. BC ICMS ST', visible: true },
      ];
      break;
    case 'pis':
    case 'cofins':
      tributeColumns = [
        { name: 'CST', visible: true },
        {
          name: `% ${window.cenariosDetalhes.currentTipoTributo.toUpperCase()}`,
          visible: true,
        },
        {
          name: `% Red. ${window.cenariosDetalhes.currentTipoTributo.toUpperCase()}`,
          visible: true,
        },
      ];
      break;
    case 'ipi':
      tributeColumns = [
        { name: 'CST', visible: true },
        { name: '% IPI', visible: true },
      ];
      break;
    case 'difal':
      tributeColumns = [
        { name: '% DIFAL ICMS', visible: true },
        { name: '% FCP', visible: true },
      ];
      break;
    default:
      tributeColumns = [
        { name: 'Tipo', visible: true },
        { name: 'Valor', visible: true },
      ];
  }

  // Combinar todas as colunas na ordem correta:
  // 1. Colunas comuns (Produto, Descrição, NCM, EX, CEST, CFOP)
  // 2. Colunas de tributo específicas
  // 3. Colunas de cliente (Estado, Cliente, etc.)
  // 4. Colunas de status (Ativo, Vigência)
  specificColumns = [...tributeColumns, ...clientColumns, ...statusColumns];

  // Adicionar coluna de ações
  return [
    ...commonColumns,
    ...specificColumns,
    { name: 'Ações', visible: true },
  ];
}

/**
 * Obtém os cabeçalhos da tabela de cenários
 * @param {string} status - Status dos cenários (novo, producao, inconsistente)
 * @returns {string} HTML com os cabeçalhos da tabela
 */
function getCenarioTableHeaders(status) {
  // Obter colunas específicas para o tipo de tributo atual
  const columns = getColumnsForTributoType(status);

  // Remover a coluna de ações, pois ela é adicionada separadamente na renderização da tabela
  const columnsWithoutActions = columns.filter((col) => col.name !== 'Ações');

  // Criar cabeçalhos da tabela
  return columnsWithoutActions
    .map(
      (column, index) =>
        `<th data-column-index="${index}" class="${
          column.visible ? '' : 'd-none'
        }">${column.name}</th>`,
    )
    .join('');
}

/**
 * Obtém as linhas da tabela de cenários
 * @param {Array} cenarios - Lista de cenários
 * @param {string} status - Status dos cenários
 * @returns {string} HTML com as linhas da tabela
 */
function getCenarioTableRows(cenarios, status) {
  if (!cenarios || cenarios.length === 0) {
    return '';
  }

  // Criar linhas da tabela
  return cenarios
    .map((cenario) => {
      // Obter valores específicos para o tipo de tributo atual
      const values = getCenarioValues(cenario, status);

      // Obter botões de ação específicos para o status
      const actionButtons = getCenarioActionButtons(cenario, status);

      // Adicionar botão de expansão para cenários inconsistentes
      const expandButton =
        status === 'inconsistente'
          ? `<button class="btn btn-sm btn-outline-info expand-comparison-btn me-1"
                data-cenario-id="${cenario.id}"
                data-produto-id="${cenario.produto_id}"
                data-cliente-id="${cenario.cliente_id}"
                data-bs-toggle="tooltip"
                title="Comparar com cenário em produção">
          <i class="fas fa-exchange-alt"></i>
        </button>`
          : '';

      // Adicionar checkbox para seleção apenas na aba "Novos"
      const checkbox =
        status === 'novo'
          ? `<td>
        <input type="checkbox" class="select-checkbox"
          data-cenario-id="${cenario.id}"
          data-status="${status}">
      </td>`
          : '';

      // Criar linha da tabela
      return `
      <tr data-cenario-id="${cenario.id}" class="${
        status === 'inconsistente' ? 'inconsistente-row' : ''
      }">
        ${checkbox}
        ${values}
        <td>${expandButton}${actionButtons}</td>
      </tr>
    `;
    })
    .join('');
}

/**
 * Obtém os valores de um cenário para a tabela
 * @param {Object} cenario - Dados do cenário
 * @param {string} status - Status dos cenários (novo, producao, inconsistente)
 * @returns {string} HTML com os valores do cenário
 */
function getCenarioValues(cenario, status) {
  // Obter as colunas para o tipo de tributo atual
  const columns = getColumnsForTributoType(status);

  // Remover a coluna de ações
  const columnsWithoutActions = columns.filter((col) => col.name !== 'Ações');

  // Criar um array para armazenar os valores das células
  const cells = [];

  // Se for um cenário inconsistente, buscar o cenário em produção para comparação
  let cenarioProducao = null;
  if (status === 'inconsistente' && window.cenariosDetalhes.producaoCenarios) {
    cenarioProducao = window.cenariosDetalhes.producaoCenarios[cenario.id];
    if (cenarioProducao) {
    } else {
    }
  }

  // Preencher os valores para cada coluna
  columnsWithoutActions.forEach((column, index) => {
    let cellValue = '';
    const visibilityClass = column.visible ? '' : 'd-none';

    switch (column.name) {
      case 'Produto':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.produto?.codigo || ''
        }</td>`;
        break;
      case 'Descrição':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.produto?.descricao || ''
        }</td>`;
        break;
      case 'NCM':
        {
          const value = cenario.ncm || '';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.ncm || '');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}</td>`;
        }
        break;
      case 'EX':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.ex || ''
        }</td>`;
        break;
      case 'CEST':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.produto?.cest || ''
        }</td>`;
        break;
      case 'CFOP':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.cfop || ''
        }</td>`;
        break;
      case 'Estado':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.cliente?.uf || ''
        }</td>`;
        break;
      case 'Cliente':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.cliente?.razao_social || cenario.cliente?.nome || ''
        }</td>`;
        break;
      case 'Simples Nacional':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.cliente?.simples_nacional
            ? '<span class="badge bg-success">Sim</span>'
            : '<span class="badge bg-secondary">Não</span>'
        }</td>`;
        break;
      case 'Atividade':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.cliente?.atividade || ''
        }</td>`;
        break;
      case 'Destinação':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.cliente?.destinacao || ''
        }</td>`;
        break;
      case 'Origem':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.origem || ''
        }</td>`;
        break;
      case 'CST':
        {
          const value = cenario.cst || '';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.cst || '');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}</td>`;
        }
        break;
      case '% ICMS':
        {
          const value = cenario.aliquota || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.aliquota || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% Red. BC ICMS':
        {
          const value = cenario.p_red_bc || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.p_red_bc || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% ICMS Diferido':
        {
          const value = cenario.p_dif || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.p_dif || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% MVA':
        {
          const value = cenario.icms_st_p_mva || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.icms_st_p_mva || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% ICMS ST':
        {
          const value = cenario.icms_st_aliquota || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent =
              value !== (cenarioProducao.icms_st_aliquota || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% Red. BC ICMS ST':
        {
          const value = cenario.icms_st_p_red_bc || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent =
              value !== (cenarioProducao.icms_st_p_red_bc || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% PIS':
        {
          const value = cenario.aliquota || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.aliquota || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% Red. PIS':
        {
          const value = cenario.p_red_bc || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.p_red_bc || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% COFINS':
        {
          const value = cenario.aliquota || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.aliquota || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% Red. COFINS':
        {
          const value = cenario.p_red_bc || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.p_red_bc || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% IPI':
        {
          const value = cenario.aliquota || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.aliquota || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% DIFAL ICMS':
        {
          const value = cenario.p_icms_uf_dest || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.p_icms_uf_dest || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case '% FCP':
        {
          const value = cenario.p_fcp_uf_dest || '0';
          let isInconsistent = false;

          // Verificar se é inconsistente comparando com o cenário em produção
          if (status === 'inconsistente' && cenarioProducao) {
            isInconsistent = value !== (cenarioProducao.p_fcp_uf_dest || '0');
          }

          const inconsistentClass = isInconsistent ? 'inconsistent-value' : '';
          cellValue = `<td class="${visibilityClass} ${inconsistentClass}" data-column-index="${index}">${value}%</td>`;
        }
        break;
      case 'Tipo':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">-</td>`;
        break;
      case 'Valor':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">-</td>`;
        break;
      case 'Ativo':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${
          cenario.ativo
            ? '<span class="badge bg-success">Sim</span>'
            : '<span class="badge bg-danger">Não</span>'
        }</td>`;
        break;
      case 'Vigência':
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">${formatVigencia(
          cenario.data_inicio_vigencia,
          cenario.data_fim_vigencia,
        )}</td>`;
        break;
      default:
        cellValue = `<td class="${visibilityClass}" data-column-index="${index}">-</td>`;
    }

    cells.push(cellValue);
  });

  return cells.join('');
}

/**
 * Formata a vigência de um cenário
 * @param {string} dataInicio - Data de início da vigência (formato YYYY-MM-DD)
 * @param {string} dataFim - Data de fim da vigência (formato YYYY-MM-DD)
 * @returns {string} Texto formatado da vigência
 */
function formatVigencia(dataInicio, dataFim) {
  if (!dataInicio) {
    return '-';
  }

  // Função auxiliar para formatar a data no formato DD/MM/YYYY
  const formatarData = (dataString) => {
    if (!dataString) return '';

    // Se a data já estiver no formato DD/MM/YYYY, retorna diretamente
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dataString)) {
      return dataString;
    }

    // Para datas no formato YYYY-MM-DD
    if (/^\d{4}-\d{2}-\d{2}/.test(dataString)) {
      const [ano, mes, dia] = dataString.split('T')[0].split('-');
      return `${dia.padStart(2, '0')}/${mes.padStart(2, '0')}/${ano}`;
    }

    // Para objetos Date ou outros formatos, tenta converter
    try {
      const data = new Date(dataString);
      // Verifica se a data é válida
      if (isNaN(data.getTime())) return dataString;

      return data.toLocaleDateString('pt-BR');
    } catch (e) {
      return dataString; // Retorna o valor original em caso de erro
    }
  };

  const inicioFormatado = formatarData(dataInicio);

  if (!dataFim) {
    return `A partir de ${inicioFormatado}`;
  }

  const fimFormatado = formatarData(dataFim);

  return `${inicioFormatado} até ${fimFormatado}`;
}

/**
 * Obtém os botões de ação para um cenário
 * @param {Object} cenario - Dados do cenário
 * @param {string} status - Status do cenário
 * @returns {string} HTML com os botões de ação
 */
function getCenarioActionButtons(cenario, status) {
  // Botões comuns para todos os status
  const editButton = `<button class="btn btn-sm btn-primary edit-cenario-btn" data-cenario-id="${cenario.id}" title="Editar"><i class="fas fa-edit"></i></button>`;

  // Botão de excluir para todos os status, exceto produção ativo
  const deleteButton =
    status !== 'producao' || !cenario.ativo
      ? `<button class="btn btn-sm btn-danger excluir-cenario-btn" data-cenario-id="${cenario.id}" title="Excluir"><i class="fas fa-trash"></i></button>`
      : '';

  // Botões específicos para cada status
  let statusButtons = '';

  switch (status) {
    case 'novo':
      statusButtons = `
        <div class="btn-group">
          <button class="btn btn-sm btn-success send-to-producao-btn" data-cenario-id="${cenario.id}" title="Enviar para Produção"><i class="fas fa-check"></i></button>
          <button class="btn btn-sm btn-warning send-to-inconsistente-btn" data-cenario-id="${cenario.id}" title="Marcar como Inconsistente"><i class="fas fa-exclamation-triangle"></i></button>
        </div>
      `;
      break;
    case 'producao':
      if (cenario.ativo) {
        statusButtons = `<button class="btn btn-sm btn-warning desativar-cenario-btn" data-cenario-id="${cenario.id}" title="Desativar"><i class="fas fa-power-off"></i></button>`;
      } else {
        statusButtons = `<button class="btn btn-sm btn-success ativar-cenario-btn" data-cenario-id="${cenario.id}" title="Ativar"><i class="fas fa-power-off"></i></button>`;
      }
      statusButtons += `<button class="btn btn-sm btn-info vigencia-cenario-btn" data-cenario-id="${cenario.id}" title="Definir Vigência"><i class="fas fa-calendar-alt"></i></button>`;
      break;
    case 'inconsistente':
      statusButtons = `
        <button class="btn btn-sm btn-success send-to-producao-btn" data-cenario-id="${cenario.id}" title="Enviar para Produção"><i class="fas fa-check"></i></button>
      `;
      break;
  }

  return `<div class="btn-group">${editButton} ${statusButtons} ${deleteButton}</div>`;
}

/**
 * Obtém os filtros específicos para o tipo de tributo atual
 * @returns {string} HTML com os filtros específicos
 */
function getTributoSpecificFilters() {
  // Removido conforme solicitado
  return '';
}

/**
 * Configura os botões da tabela de cenários
 */
function setupCenarioButtons() {
  // Botão de editar cenário
  document.querySelectorAll('.edit-cenario-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      editCenarioDetails(cenarioId);
    });
  });

  // Botão de enviar para produção
  document.querySelectorAll('.send-to-producao-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      updateCenarioStatus(cenarioId, 'producao');
    });
  });

  // Botão de marcar como inconsistente
  document.querySelectorAll('.send-to-inconsistente-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      updateCenarioStatus(cenarioId, 'inconsistente');
    });
  });

  // Botão de excluir cenário
  document.querySelectorAll('.excluir-cenario-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      excluirCenario(cenarioId);
    });
  });

  // Botão de ativar cenário
  document.querySelectorAll('.ativar-cenario-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      ativarCenario(cenarioId);
    });
  });

  // Botão de desativar cenário
  document.querySelectorAll('.desativar-cenario-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      desativarCenario(cenarioId);
    });
  });

  // Botão de definir vigência
  document.querySelectorAll('.vigencia-cenario-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      showVigenciaModal(cenarioId);
    });
  });

  // Botão de expandir comparação
  document.querySelectorAll('.expand-comparison-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const cenarioId = this.dataset.cenarioId;
      toggleCenarioComparison(cenarioId);
    });
  });

  // Inicializar tooltips
  const tooltipTriggerList = [].slice.call(
    document.querySelectorAll('[data-bs-toggle="tooltip"]'),
  );
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Configurar checkboxes de seleção apenas para a aba "Novos"
  const activeTab = document.querySelector('.nav-link.active');
  if (activeTab && activeTab.id === 'novo-tab') {
    setupSelectionCheckboxes();
  }
}

/**
 * Configura os checkboxes de seleção
 */
function setupSelectionCheckboxes() {
  console.log('=== Iniciando setupSelectionCheckboxes ===');

  // Obter o status atual da aba ativa
  const activeTab = $('.nav-link.active').attr('id');
  console.log('Aba ativa:', activeTab);

  let status = 'novo'; // Valor padrão
  if (activeTab === 'producao-tab') {
    status = 'producao';
  } else if (activeTab === 'inconsistente-tab') {
    status = 'inconsistente';
  }

  console.log('Status determinado:', status);

  // Limpar qualquer evento existente para evitar duplicação
  console.log('Removendo event listeners antigos...');
  $(document).off('change', '.select-checkbox');
  $(document).off('change', '.select-all-checkbox');
  $(document).off('click', '[id^="select-all-btn-"]');
  $(document).off('click', '[id^="clear-selection-btn-"]');

  // Configurar checkboxes individuais usando delegação de eventos
  $(document).on('change', '.select-checkbox', function () {
    console.log('Checkbox alterado:', this);
    const cenarioId = parseInt($(this).data('cenario-id'));
    const status = $(this).data('status') || 'novo';

    console.log('ID do cenário:', cenarioId, 'Status:', status);

    // Atualizar array de cenários selecionados
    if ($(this).is(':checked')) {
      console.log('Checkbox marcado');
      // Adicionar ao array se não existir
      if (
        !window.cenariosDetalhes.selectedCenarios[status].includes(cenarioId)
      ) {
        window.cenariosDetalhes.selectedCenarios[status].push(cenarioId);
      }
      // Adicionar classe selected à linha
      $(this).closest('tr').addClass('selected');
    } else {
      console.log('Checkbox desmarcado');
      // Remover do array
      window.cenariosDetalhes.selectedCenarios[status] =
        window.cenariosDetalhes.selectedCenarios[status].filter(
          (id) => id !== cenarioId,
        );
      // Remover classe selected da linha
      $(this).closest('tr').removeClass('selected');
      // Desmarcar o checkbox "selecionar todos"
      $(`#select-all-checkbox-${status}`).prop('checked', false);
    }

    console.log(
      'Cenários selecionados:',
      window.cenariosDetalhes.selectedCenarios[status],
    );

    // Atualizar contador de seleção
    updateSelectionCount(status);
    // Atualizar estado dos botões de ação em lote
    updateBatchButtonsState(status);
  });

  // Configurar checkbox "selecionar todos"
  $(document).on('change', '.select-all-checkbox', function () {
    console.log('Select All alterado:', this);
    const status = $(this).attr('id').replace('select-all-checkbox-', '');
    const isChecked = $(this).is(':checked');

    console.log('Status:', status, 'isChecked:', isChecked);

    // Limpar array de selecionados
    window.cenariosDetalhes.selectedCenarios[status] = [];

    // Marcar/desmarcar todos os checkboxes visíveis
    const tableId = `#cenario-${status}-table`;
    const $checkboxes = $(`${tableId} tbody .select-checkbox`);

    console.log(
      'Encontrados',
      $checkboxes.length,
      'checkboxes na tabela',
      tableId,
    );

    $checkboxes.each(function (index) {
      const $checkbox = $(this);
      const cenarioId = parseInt($checkbox.data('cenario-id'));

      console.log(
        `Processando checkbox ${index + 1}:`,
        $checkbox[0],
        'ID:',
        cenarioId,
      );

      // Atualizar estado visual
      $checkbox.prop('checked', isChecked);

      // Atualizar array de selecionados
      if (isChecked) {
        window.cenariosDetalhes.selectedCenarios[status].push(cenarioId);
        $checkbox.closest('tr').addClass('selected');
      } else {
        $checkbox.closest('tr').removeClass('selected');
      }
    });

    // Se estiver desmarcando, limpar o array
    if (!isChecked) {
      window.cenariosDetalhes.selectedCenarios[status] = [];
    }

    console.log(
      'Cenários selecionados após seleção:',
      window.cenariosDetalhes.selectedCenarios[status],
    );

    // Atualizar contador de seleção
    updateSelectionCount(status);
    // Atualizar estado dos botões de ação em lote
    updateBatchButtonsState(status);
  });

  // Configurar clique no botão "Selecionar Todos"
  $(document).on('click', '[id^="select-all-btn-"]', function (e) {
    e.preventDefault();
    console.log('Botão Selecionar Todos clicado');
    const status = $(this).attr('id').replace('select-all-btn-', '');
    const $selectAllCheckbox = $(`#select-all-checkbox-${status}`);
    console.log(
      'Checkbox select-all encontrado:',
      $selectAllCheckbox.length > 0,
    );

    // Marcar o checkbox como selecionado e disparar o evento change
    $selectAllCheckbox.prop('checked', true).trigger('change');
  });

  // Configurar clique no botão "Limpar Seleção"
  $(document).on('click', '[id^="clear-selection-btn-"]', function (e) {
    e.preventDefault();
    console.log('Botão Limpar Seleção clicado');
    const status = $(this).attr('id').replace('clear-selection-btn-', '');

    console.log('Desmarcando todos os checkboxes...');
    // Desmarcar todos os checkboxes
    const $checkboxes = $(`#cenario-${status}-table tbody .select-checkbox`);
    $checkboxes.prop('checked', false);

    // Desmarcar o checkbox "Selecionar Todos"
    $(`#select-all-checkbox-${status}`).prop('checked', false);

    // Limpar array de selecionados
    window.cenariosDetalhes.selectedCenarios[status] = [];

    // Remover classe selected de todas as linhas
    $(`#cenario-${status}-table tbody tr`).removeClass('selected');

    console.log(
      'Seleção limpa. Cenários selecionados:',
      window.cenariosDetalhes.selectedCenarios[status],
    );

    // Atualizar contador e botões
    updateSelectionCount(status);
    updateBatchButtonsState(status);
  });

  console.log('=== Finalizando setupSelectionCheckboxes ===');
}

/**
 * Atualiza o contador de cenários selecionados
 * @param {string} status - Status dos cenários
 */
function updateSelectionCount(status) {
  const count = window.cenariosDetalhes.selectedCenarios[status].length;
  $(`#selection-count-${status}`).text(count);
}

/**
 * Atualiza o estado dos botões de ação em lote
 * @param {string} status - Status dos cenários
 */
function updateBatchButtonsState(status) {
  const count = window.cenariosDetalhes.selectedCenarios[status].length;

  // Habilitar/desabilitar botões com base na contagem
  $(`#batch-to-production-btn-${status}`).prop('disabled', count === 0);
  $(`#batch-to-inconsistent-btn-${status}`).prop('disabled', count === 0);
}

/**
 * Configura os botões de ação em lote
 * @param {string} status - Status dos cenários
 * @param {Array} cenarios - Lista de cenários
 */
function setupBatchActionButtons(status, cenarios) {
  // Não configurar os botões "Selecionar Todos" e "Limpar Seleção" aqui
  // Eles são configurados em setupSelectionCheckboxes() para evitar duplicação

  // Configurar botão "Enviar para Produção"
  $(`#batch-to-production-btn-${status}`)
    .off('click')
    .on('click', function () {
      batchUpdateStatus(status, 'producao');
    });

  // Configurar botão "Marcar como Inconsistente"
  $(`#batch-to-inconsistent-btn-${status}`)
    .off('click')
    .on('click', function () {
      batchUpdateStatus(status, 'inconsistente');
    });
}

/**
 * Alterna a exibição da comparação entre cenário inconsistente e cenário em produção
 * @param {number} cenarioId - ID do cenário inconsistente
 */
function toggleCenarioComparison(cenarioId) {
  const button = document.querySelector(
    `.expand-comparison-btn[data-cenario-id="${cenarioId}"]`,
  );
  if (!button) return;

  // Verificar se já existe uma linha de comparação
  const existingComparisonRow = document.getElementById(
    `comparison-row-${cenarioId}`,
  );

  if (!existingComparisonRow) {
    // Não existe linha de comparação, vamos criar uma
    const produtoId = button.dataset.produtoId;
    const clienteId = button.dataset.clienteId;

    // Alterar o ícone do botão
    button.innerHTML = '<i class="fas fa-times"></i>';
    button.classList.remove('btn-outline-info');
    button.classList.add('btn-outline-danger');
    button.setAttribute('title', 'Fechar comparação');

    // Atualizar o tooltip
    const tooltip = bootstrap.Tooltip.getInstance(button);
    if (tooltip) {
      tooltip.dispose();
    }
    new bootstrap.Tooltip(button);

    // Buscar o cenário em produção
    loadCenarioProducao(cenarioId, produtoId, clienteId);
  } else {
    // Já existe uma linha de comparação, vamos removê-la
    existingComparisonRow.remove();

    // Alterar o ícone do botão
    button.innerHTML = '<i class="fas fa-exchange-alt"></i>';
    button.classList.remove('btn-outline-danger');
    button.classList.add('btn-outline-info');
    button.setAttribute('title', 'Comparar com cenário em produção');

    // Atualizar o tooltip
    const tooltip = bootstrap.Tooltip.getInstance(button);
    if (tooltip) {
      tooltip.dispose();
    }
    new bootstrap.Tooltip(button);
  }
}

/**
 * Carrega o cenário em produção correspondente e adiciona uma linha na tabela
 * @param {number} cenarioId - ID do cenário inconsistente
 * @param {number} produtoId - ID do produto
 * @param {number} clienteId - ID do cliente
 */
function loadCenarioProducao(cenarioId, produtoId, clienteId) {
  // Mostrar indicador de carregamento
  const inconsistenteRow = document.querySelector(
    `tr[data-cenario-id="${cenarioId}"]`,
  );
  if (!inconsistenteRow) return;

  // Adicionar uma linha temporária com indicador de carregamento
  const loadingRow = document.createElement('tr');
  loadingRow.id = `comparison-row-${cenarioId}`;
  loadingRow.className = 'comparison-row';
  loadingRow.innerHTML = `
    <td colspan="100%" class="text-center py-3">
      <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
      <span class="ms-2">Carregando cenário em produção...</span>
    </td>
  `;

  // Inserir após a linha do cenário inconsistente
  inconsistenteRow.after(loadingRow);

  // Buscar o cenário em produção correspondente
  const params = new URLSearchParams();
  params.append('status', 'producao');
  params.append('produto_id', produtoId);
  params.append('cliente_id', clienteId);

  fetch(
    `/api/cenarios/${
      window.cenariosDetalhes.currentTipoTributo
    }?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.cenarios && data.cenarios.length > 0) {
        // Encontrou cenário em produção para comparação
        const cenarioProducao = data.cenarios[0];

        // Obter valores específicos para o tipo de tributo atual
        const values = getCenarioValues(cenarioProducao, 'producao');

        // Criar botões de ação
        const actionButtons = `
          <button class="btn btn-sm btn-success update-production-btn"
                  data-cenario-id="${cenarioId}"
                  data-bs-toggle="tooltip"
                  title="Atualizar produção com este cenário">
            <i class="fas fa-check"></i>
          </button>
          <button class="btn btn-sm btn-danger excluir-cenario-btn ms-1"
                  data-cenario-id="${cenarioId}"
                  data-bs-toggle="tooltip"
                  title="Excluir cenário">
            <i class="fas fa-trash"></i>
          </button>
          <button class="btn btn-sm btn-outline-secondary show-differences-btn ms-1"
                  data-cenario-id="${cenarioId}"
                  data-bs-toggle="tooltip"
                  title="Mostrar diferenças">
            <i class="fas fa-search"></i>
          </button>
        `;

        // Substituir a linha de carregamento por uma linha de tabela normal
        // com a mesma estrutura que a linha do cenário inconsistente
        loadingRow.className = 'cenario-producao-row';
        loadingRow.innerHTML = `
          ${values}
          <td>
            <span class="cenario-producao-badge">Produção</span>
            ${actionButtons}
          </td>
        `;

        // Configurar eventos para os botões
        loadingRow
          .querySelector('.update-production-btn')
          .addEventListener('click', function () {
            updateCenarioStatus(cenarioId, 'producao');
          });

        loadingRow
          .querySelector('.excluir-cenario-btn')
          .addEventListener('click', function () {
            excluirCenario(cenarioId);
          });

        loadingRow
          .querySelector('.show-differences-btn')
          .addEventListener('click', function () {
            showCenarioDifferences(cenarioId);
          });

        // Inicializar tooltips
        const tooltipTriggerList = [].slice.call(
          loadingRow.querySelectorAll('[data-bs-toggle="tooltip"]'),
        );
        tooltipTriggerList.map(function (tooltipTriggerEl) {
          return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Armazenar o cenário em produção para uso posterior
        window.cenariosDetalhes.producaoCenarios =
          window.cenariosDetalhes.producaoCenarios || {};
        window.cenariosDetalhes.producaoCenarios[cenarioId] = cenarioProducao;
      } else {
        // Não encontrou cenário em produção
        loadingRow.innerHTML = `
          <td colspan="100%">
            <div class="alert alert-warning mb-0">
              <i class="fas fa-exclamation-triangle me-2"></i> Não foi encontrado cenário em produção para este produto e cliente.
            </div>
          </td>
        `;
      }
    })
    .catch((error) => {
      loadingRow.innerHTML = `
        <td colspan="100%">
          <div class="alert alert-danger mb-0">
            <i class="fas fa-exclamation-circle me-2"></i> Erro ao carregar dados de comparação: ${error.message}
          </div>
        </td>
      `;
    });
}

/**
 * Mostra as diferenças entre o cenário inconsistente e o cenário em produção
 * @param {number} cenarioInconsistenteId - ID do cenário inconsistente
 */
function showCenarioDifferences(cenarioInconsistenteId) {
  // Buscar os detalhes do cenário inconsistente
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioInconsistenteId}`,
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.cenario) {
        const cenarioInconsistente = data.cenario;
        const cenarioProducao =
          window.cenariosDetalhes.producaoCenarios[cenarioInconsistenteId];

        if (cenarioProducao) {
          // Criar modal para mostrar as diferenças
          const modal = document.createElement('div');
          modal.className = 'modal fade';
          modal.id = `differences-modal-${cenarioInconsistenteId}`;
          modal.setAttribute('tabindex', '-1');
          modal.setAttribute(
            'aria-labelledby',
            `differences-modal-label-${cenarioInconsistenteId}`,
          );
          modal.setAttribute('aria-hidden', 'true');

          // Obter campos para comparação com base no tipo de tributo
          const comparisonFields = getCenarioComparisonFields();

          // Criar linhas da tabela de comparação
          const comparisonRows = comparisonFields
            .map((field) => {
              const inconsistenteValue = getFieldValue(
                cenarioInconsistente,
                field,
              );
              const producaoValue = getFieldValue(cenarioProducao, field);
              const isDifferent = inconsistenteValue !== producaoValue;

              if (!isDifferent) return ''; // Mostrar apenas as diferenças

              return `
              <tr class="table-danger">
                <td><strong>${field.label}</strong></td>
                <td class="text-danger fw-bold">${formatFieldValue(
                  inconsistenteValue,
                  field.type,
                )}</td>
                <td>${formatFieldValue(producaoValue, field.type)}</td>
              </tr>
            `;
            })
            .filter((row) => row !== '') // Remover linhas vazias
            .join('');

          // Se não houver diferenças, mostrar mensagem
          const tableContent =
            comparisonRows ||
            `
            <tr>
              <td colspan="3" class="text-center">
                <div class="alert alert-success mb-0">
                  <i class="fas fa-check-circle me-2"></i> Não há diferenças entre os cenários.
                </div>
              </td>
            </tr>
          `;

          // Conteúdo do modal
          modal.innerHTML = `
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                  <h5 class="modal-title" id="differences-modal-label-${cenarioInconsistenteId}">
                    Diferenças entre Cenários
                  </h5>
                  <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover mb-0">
                      <thead class="table-primary">
                        <tr>
                          <th style="width: 30%">Campo</th>
                          <th style="width: 35%">Valor Inconsistente</th>
                          <th style="width: 35%">Valor em Produção</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${tableContent}
                      </tbody>
                    </table>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                  <button type="button" class="btn btn-success update-production-btn" data-cenario-id="${cenarioInconsistenteId}">
                    <i class="fas fa-check me-1"></i> Atualizar Produção
                  </button>
                  <button type="button" class="btn btn-outline-danger excluir-cenario-btn" data-cenario-id="${cenarioInconsistenteId}">
                    <i class="fas fa-trash me-1"></i> Excluir Cenário
                  </button>
                </div>
              </div>
            </div>
          `;

          // Adicionar o modal ao corpo do documento
          document.body.appendChild(modal);

          // Inicializar o modal
          const modalInstance = new bootstrap.Modal(modal);
          modalInstance.show();

          // Configurar eventos para os botões
          modal
            .querySelector('.update-production-btn')
            .addEventListener('click', function () {
              updateCenarioStatus(cenarioInconsistenteId, 'producao');
              modalInstance.hide();
            });

          modal
            .querySelector('.excluir-cenario-btn')
            .addEventListener('click', function () {
              excluirCenario(cenarioInconsistenteId);
              modalInstance.hide();
            });

          // Remover o modal do DOM quando for fechado
          modal.addEventListener('hidden.bs.modal', function () {
            modal.remove();
          });
        } else {
          alert('Erro: Cenário em produção não encontrado.');
        }
      } else {
        alert('Erro ao carregar detalhes do cenário inconsistente.');
      }
    })
    .catch((error) => {
      alert('Erro ao carregar detalhes do cenário inconsistente.');
    });
}

/**
 * Obtém os campos para comparação entre cenários com base no tipo de tributo
 * @returns {Array} Lista de campos para comparação
 */
function getCenarioComparisonFields() {
  // Campos comuns para todos os tipos de tributo
  const commonFields = [
    { field: 'produto.codigo', label: 'Código do Produto', type: 'text' },
    { field: 'produto.descricao', label: 'Descrição do Produto', type: 'text' },
    { field: 'ncm', label: 'NCM', type: 'text' },
    { field: 'cfop', label: 'CFOP', type: 'text' },
    { field: 'cliente.razao_social', label: 'Cliente', type: 'text' },
    { field: 'cliente.uf', label: 'Estado', type: 'text' },
  ];

  // Campos específicos para cada tipo de tributo
  let specificFields = [];

  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      specificFields = [
        { field: 'origem', label: 'Origem', type: 'text' },
        { field: 'cst', label: 'CST', type: 'text' },
        { field: 'aliquota', label: 'Alíquota ICMS (%)', type: 'percentage' },
        { field: 'p_red_bc', label: 'Redução BC ICMS (%)', type: 'percentage' },
        { field: 'p_dif', label: 'ICMS Diferido (%)', type: 'percentage' },
      ];
      break;
    case 'icms_st':
      specificFields = [
        { field: 'origem', label: 'Origem', type: 'text' },
        { field: 'cst', label: 'CST', type: 'text' },
        { field: 'aliquota', label: 'Alíquota ICMS (%)', type: 'percentage' },
        { field: 'p_red_bc', label: 'Redução BC ICMS (%)', type: 'percentage' },
        { field: 'icms_st_p_mva', label: 'MVA (%)', type: 'percentage' },
        {
          field: 'icms_st_aliquota',
          label: 'Alíquota ICMS-ST (%)',
          type: 'percentage',
        },
        {
          field: 'icms_st_p_red_bc',
          label: 'Redução BC ICMS-ST (%)',
          type: 'percentage',
        },
      ];
      break;
    case 'ipi':
      specificFields = [
        { field: 'cst', label: 'CST', type: 'text' },
        {
          field: 'codigo_enquadramento',
          label: 'Código Enquadramento',
          type: 'text',
        },
        { field: 'aliquota', label: 'Alíquota IPI (%)', type: 'percentage' },
      ];
      break;
    case 'pis':
      specificFields = [
        { field: 'cst', label: 'CST', type: 'text' },
        { field: 'aliquota', label: 'Alíquota PIS (%)', type: 'percentage' },
        { field: 'p_red_bc', label: 'Redução BC PIS (%)', type: 'percentage' },
      ];
      break;
    case 'cofins':
      specificFields = [
        { field: 'cst', label: 'CST', type: 'text' },
        { field: 'aliquota', label: 'Alíquota COFINS (%)', type: 'percentage' },
        {
          field: 'p_red_bc',
          label: 'Redução BC COFINS (%)',
          type: 'percentage',
        },
      ];
      break;
    case 'difal':
      specificFields = [
        {
          field: 'p_fcp_uf_dest',
          label: 'FCP UF Destino (%)',
          type: 'percentage',
        },
        {
          field: 'p_icms_uf_dest',
          label: 'ICMS UF Destino (%)',
          type: 'percentage',
        },
        {
          field: 'p_icms_inter',
          label: 'ICMS Interestadual (%)',
          type: 'percentage',
        },
        {
          field: 'p_icms_inter_part',
          label: 'Percentual Partilha (%)',
          type: 'percentage',
        },
      ];
      break;
    default:
      specificFields = [];
  }

  return [...commonFields, ...specificFields];
}

/**
 * Edita os detalhes de um cenário
 * @param {number} cenarioId - ID do cenário
 */
function editCenarioDetails(cenarioId) {
  // Fazer requisição para obter os detalhes do cenário
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioId}`,
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.cenario) {
        // Usar o modal aprimorado em vez do modal antigo
        // O modal aprimorado está definido em cenario_modal_enhanced.js
        if (typeof showCenarioModal === 'function') {
          showCenarioModal(data.cenario);
        } else {
          alert(
            'Erro ao abrir o modal de cenário. Por favor, recarregue a página e tente novamente.',
          );
        }
      } else {
        alert('Erro ao carregar detalhes do cenário.');
      }
    })
    .catch((error) => {
      alert('Erro ao carregar detalhes do cenário.');
    });
}

/**
 * Atualiza o status de um cenário
 * @param {number} cenarioId - ID do cenário
 * @param {string} newStatus - Novo status para o cenário (default: 'producao')
 */
function updateCenarioStatus(cenarioId, newStatus = 'producao') {
  // Atribuir a função ao namespace global para que possa ser acessada de outros arquivos
  window.cenariosDetalhes.updateCenarioStatus = updateCenarioStatus;

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert(
      'Por favor, selecione uma empresa antes de atualizar o status do cenário.',
    );
    return;
  }

  const statusText = newStatus === 'producao' ? 'produção' : newStatus;
  if (!confirm(`Deseja realmente enviar este cenário para ${statusText}?`)) {
    return;
  }

  // Obter o ID da empresa e do escritório
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = parseInt(currentUser.escritorio_id || '0');

  // Verificar se os IDs são válidos
  if (isNaN(empresaId) || empresaId <= 0) {
    alert('ID da empresa inválido. Por favor, selecione uma empresa válida.');
    return;
  }

  if (isNaN(escritorioId) || escritorioId <= 0) {
    alert('ID do escritório não encontrado. Por favor, faça login novamente.');
    return;
  }

  // Se o novo status for 'producao', adicionar a data de início de vigência atual
  const requestData = {
    empresa_id: empresaId,
    escritorio_id: escritorioId,
    status: newStatus,
  };

  // Adicionar data de início de vigência automaticamente para status 'producao'
  if (newStatus === 'producao') {
    // Obter a data atual no formato ISO (YYYY-MM-DD)
    const today = new Date();
    const dataInicioVigencia = today.toISOString().split('T')[0];
    requestData.data_inicio_vigencia = dataInicioVigencia;
  }

  // Fazer requisição para atualizar o status do cenário
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioId}/status`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(requestData),
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert(data.message || 'Status atualizado com sucesso!');

        // Recarregar os dados para todas as abas
        loadCenarioData('novo');
        loadCenarioData('producao');
        loadCenarioData('inconsistente');
        loadCenarioData('conforme');
      } else {
        alert(data.message || 'Erro ao atualizar status do cenário.');
      }
    })
    .catch((error) => {
      alert('Erro ao atualizar status do cenário.');
    });
}

/**
 * Ativa um cenário
 * @param {number} cenarioId - ID do cenário
 */
function ativarCenario(cenarioId) {
  // Atribuir a função ao namespace global para que possa ser acessada de outros arquivos
  window.cenariosDetalhes.ativarCenario = ativarCenario;

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert('Por favor, selecione uma empresa antes de ativar o cenário.');
    return;
  }

  if (
    !confirm(
      'Deseja realmente ativar este cenário? Isso desativará outros cenários do mesmo tipo para o mesmo produto/cliente.',
    )
  ) {
    return;
  }

  // Obter o ID da empresa e do escritório
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = parseInt(currentUser.escritorio_id || '0');

  // Verificar se os IDs são válidos
  if (isNaN(empresaId) || empresaId <= 0) {
    alert('ID da empresa inválido. Por favor, selecione uma empresa válida.');
    return;
  }

  if (isNaN(escritorioId) || escritorioId <= 0) {
    alert('ID do escritório não encontrado. Por favor, faça login novamente.');
    return;
  }

  // Fazer requisição para ativar o cenário
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioId}/ativar`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify({
        empresa_id: empresaId,
        escritorio_id: escritorioId,
      }),
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert('Cenário ativado com sucesso!');

        // Recarregar os dados para a aba de produção
        loadCenarioData('producao');
      } else {
        alert(data.message || 'Erro ao ativar cenário.');
      }
    })
    .catch((error) => {
      alert('Erro ao ativar cenário.');
    });
}

/**
 * Desativa um cenário
 * @param {number} cenarioId - ID do cenário
 */
function desativarCenario(cenarioId) {
  // Atribuir a função ao namespace global para que possa ser acessada de outros arquivos
  window.cenariosDetalhes.desativarCenario = desativarCenario;

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert('Por favor, selecione uma empresa antes de desativar o cenário.');
    return;
  }

  if (!confirm('Deseja realmente desativar este cenário?')) {
    return;
  }

  // Obter o ID da empresa e do escritório
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = parseInt(currentUser.escritorio_id || '0');

  // Verificar se os IDs são válidos
  if (isNaN(empresaId) || empresaId <= 0) {
    alert('ID da empresa inválido. Por favor, selecione uma empresa válida.');
    return;
  }

  if (isNaN(escritorioId) || escritorioId <= 0) {
    alert('ID do escritório não encontrado. Por favor, faça login novamente.');
    return;
  }

  // Fazer requisição para desativar o cenário
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioId}/desativar`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify({
        empresa_id: empresaId,
        escritorio_id: escritorioId,
      }),
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert('Cenário desativado com sucesso!');

        // Recarregar os dados para a aba de produção
        loadCenarioData('producao');
      } else {
        alert(data.message || 'Erro ao desativar cenário.');
      }
    })
    .catch((error) => {
      alert('Erro ao desativar cenário.');
    });
}

/**
 * Exibe o modal para definir a vigência de um cenário
 * @param {number} cenarioId - ID do cenário
 */
function showVigenciaModal(cenarioId) {
  // Verificar se o modal já existe
  let modal = document.getElementById('vigencia-modal');

  // Se não existir, criar o modal
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'vigencia-modal';
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.setAttribute('aria-labelledby', 'vigencia-modal-label');
    modal.setAttribute('aria-hidden', 'true');

    modal.innerHTML = `
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="vigencia-modal-label">Definir Vigência do Cenário</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
          </div>
          <div class="modal-body">
            <form id="vigencia-form">
              <div class="mb-3">
                <label for="data-inicio-vigencia" class="form-label">Data de Início de Vigência</label>
                <input type="date" class="form-control" id="data-inicio-vigencia" required>
              </div>
              <div class="mb-3">
                <label for="data-fim-vigencia" class="form-label">Data de Fim de Vigência (opcional)</label>
                <input type="date" class="form-control" id="data-fim-vigencia">
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button type="button" class="btn btn-primary" id="salvar-vigencia-btn">Salvar</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  // Inicializar o modal Bootstrap
  const modalInstance = new bootstrap.Modal(modal);

  // Preencher a data de início de vigência com a data atual
  const today = new Date();
  const dataInicioVigenciaInput = document.getElementById(
    'data-inicio-vigencia',
  );
  dataInicioVigenciaInput.value = today.toISOString().split('T')[0];

  // Configurar o botão de salvar
  document.getElementById('salvar-vigencia-btn').onclick = function () {
    const dataInicioVigencia = dataInicioVigenciaInput.value;
    const dataFimVigencia = document.getElementById('data-fim-vigencia').value;

    if (!dataInicioVigencia) {
      alert('A data de início de vigência é obrigatória.');
      return;
    }

    // Atualizar a vigência do cenário
    atualizarVigenciaCenario(cenarioId, dataInicioVigencia, dataFimVigencia);

    // Fechar o modal
    modalInstance.hide();
  };

  // Exibir o modal
  modalInstance.show();
}

/**
 * Atualiza a vigência de um cenário
 * @param {number} cenarioId - ID do cenário
 * @param {string} dataInicioVigencia - Data de início de vigência (formato ISO)
 * @param {string} dataFimVigencia - Data de fim de vigência (formato ISO, opcional)
 */
function atualizarVigenciaCenario(
  cenarioId,
  dataInicioVigencia,
  dataFimVigencia = null,
) {
  // Atribuir a função ao namespace global para que possa ser acessada de outros arquivos
  window.cenariosDetalhes.atualizarVigenciaCenario = atualizarVigenciaCenario;

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert(
      'Por favor, selecione uma empresa antes de atualizar a vigência do cenário.',
    );
    return;
  }

  // Obter o ID da empresa e do escritório
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = parseInt(currentUser.escritorio_id || '0');

  // Verificar se os IDs são válidos
  if (isNaN(empresaId) || empresaId <= 0) {
    alert('ID da empresa inválido. Por favor, selecione uma empresa válida.');
    return;
  }

  if (isNaN(escritorioId) || escritorioId <= 0) {
    alert('ID do escritório não encontrado. Por favor, faça login novamente.');
    return;
  }

  // Fazer requisição para atualizar a vigência do cenário
  fetch(
    `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioId}/vigencia`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify({
        empresa_id: empresaId,
        escritorio_id: escritorioId,
        data_inicio_vigencia: dataInicioVigencia,
        data_fim_vigencia: dataFimVigencia,
      }),
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert('Vigência do cenário atualizada com sucesso!');

        // Recarregar os dados para a aba de produção
        loadCenarioData('producao');
      } else {
        alert(data.message || 'Erro ao atualizar vigência do cenário.');
      }
    })
    .catch((error) => {
      alert('Erro ao atualizar vigência do cenário.');
    });
}

/**
 * Reverifica automaticamente o status de tributos relacionados após um tributo ser enviado para produção
 * @param {number} tributoId - ID do tributo que foi atualizado para produção
 */
function reverificarStatusTributosAutomatico(tributoId) {
  // Verificar se há uma empresa selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    console.error('Empresa não selecionada para reverificação automática');
    return;
  }

  // Obter o ID da empresa
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Verificar se o ID é válido
  if (isNaN(empresaId) || empresaId <= 0) {
    return;
  }

  // Primeiro, obter os detalhes do tributo para saber o produto_id
  fetch(`/api/tributos/${tributoId}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((tributoData) => {
      if (!tributoData.tributo) {
        return;
      }

      const produtoId = tributoData.tributo.produto_id;

      if (!produtoId) {
        return;
      }

      // Preparar dados para a requisição de reverificação
      const data = {
        empresa_id: empresaId,
        tipo_tributo: window.cenariosDetalhes.currentTipoTributo,
        produto_id: produtoId, // Adicionar produto_id para limitar a reverificação apenas a este produto
      };

      // Fazer requisição para a API
      fetch('/api/tributos/reverificar-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(data),
      })
        .then((response) => response.json())
        .then((data) => {
          // Mostrar resultado
          if (data.message) {
            // Recarregar os dados para todas as abas
            loadCenarioData('novo');
            loadCenarioData('producao');
            loadCenarioData('inconsistente');
            loadCenarioData('conforme');
          } else {
          }
        })
        .catch((error) => {});
    })
    .catch((error) => {});
}

// A função showTributoModal foi movida para tributo_modal_enhanced.js

/**
 * Carrega dados de comparação com a versão em produção
 * @param {Object} tributo - Dados do tributo inconsistente
 */
function loadProducaoComparisonData(tributo) {
  const container = document.getElementById('producao-comparison-container');
  if (!container) return;

  // Mostrar indicador de carregamento
  container.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
    </div>
  `;

  // Buscar tributos em produção para o mesmo produto e cliente
  const params = new URLSearchParams();
  params.append('tipo_tributo', window.cenariosDetalhes.currentTipoTributo);
  params.append('status', 'producao');
  params.append('produto_id', tributo.produto_id);
  params.append('cliente_id', tributo.cliente_id);

  fetch(`/api/tributos?${params.toString()}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.tributos && data.tributos.length > 0) {
        // Encontrou tributo em produção para comparação
        const tributoProducao = data.tributos[0];
        renderComparisonTable(container, tributo, tributoProducao);
      } else {
        // Não encontrou tributo em produção
        container.innerHTML = `
          <div class="alert alert-warning">
            Não foi encontrado tributo em produção para este produto e cliente.
          </div>
        `;
      }
    })
    .catch((error) => {
      container.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar dados de comparação: ${error.message}
        </div>
      `;
    });
}

/**
 * Renderiza a tabela de comparação entre tributo inconsistente e produção
 * @param {HTMLElement} container - Container onde a tabela será renderizada
 * @param {Object} tributoInconsistente - Dados do tributo inconsistente
 * @param {Object} tributoProducao - Dados do tributo em produção
 */
function renderComparisonTable(
  container,
  tributoInconsistente,
  tributoProducao,
) {
  // Obter campos para comparação com base no tipo de tributo
  const comparisonFields = getComparisonFields();

  // Criar linhas da tabela de comparação
  const comparisonRows = comparisonFields
    .map((field) => {
      const inconsistenteValue = getFieldValue(tributoInconsistente, field);
      const producaoValue = getFieldValue(tributoProducao, field);
      const isDifferent = inconsistenteValue !== producaoValue;

      return `
      <tr class="${isDifferent ? 'table-danger' : ''}">
        <td>${field.label}</td>
        <td>${formatFieldValue(inconsistenteValue, field.type)}</td>
        <td>${formatFieldValue(producaoValue, field.type)}</td>
      </tr>
    `;
    })
    .join('');

  // Renderizar tabela de comparação
  container.innerHTML = `
    <div class="table-responsive">
      <table class="table table-bordered">
        <thead>
          <tr class="table-primary">
            <th>Campo</th>
            <th>Valor Atual</th>
            <th>Valor em Produção</th>
          </tr>
        </thead>
        <tbody>
          ${comparisonRows}
        </tbody>
      </table>
    </div>
    <div class="d-flex justify-content-end mt-3">
      <button class="btn btn-success me-2" id="update-production-btn" data-tributo-id="${tributoInconsistente.id}">
        Atualizar Produção
      </button>
      <button class="btn btn-secondary" id="mark-as-conforme-btn" data-tributo-id="${tributoInconsistente.id}">
        Marcar como Conforme
      </button>
    </div>
  `;

  // Configurar eventos para os botões
  document
    .getElementById('update-production-btn')
    .addEventListener('click', function () {
      const tributoId = this.dataset.tributoId;
      sendToProducao(tributoId, 'producao');
    });

  document
    .getElementById('mark-as-conforme-btn')
    .addEventListener('click', function () {
      const tributoId = this.dataset.tributoId;
      sendToProducao(tributoId, 'conforme');
    });
}

/**
 * Obtém os campos para comparação com base no tipo de tributo
 * @returns {Array} Lista de campos para comparação
 */
function getComparisonFields() {
  // Campos comuns para todos os tipos de tributo
  const commonFields = [
    { field: 'produto.codigo', label: 'Código do Produto', type: 'text' },
    { field: 'produto.descricao', label: 'Descrição do Produto', type: 'text' },
    { field: 'ncm', label: 'NCM', type: 'text' },
    { field: 'cfop', label: 'CFOP', type: 'text' },
  ];

  // Campos específicos para cada tipo de tributo
  let specificFields = [];

  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      specificFields = [
        { field: 'icms_origem', label: 'Origem', type: 'text' },
        { field: 'icms_cst', label: 'CST', type: 'text' },
        { field: 'icms_aliquota', label: 'Alíquota ICMS', type: 'percentage' },
        {
          field: 'icms_p_red_bc',
          label: 'Redução BC ICMS',
          type: 'percentage',
        },
        { field: 'icms_vbc', label: 'Base de Cálculo ICMS', type: 'currency' },
        { field: 'icms_valor', label: 'Valor ICMS', type: 'currency' },
      ];
      break;
    case 'icms_st':
      specificFields = [
        { field: 'icms_origem', label: 'Origem', type: 'text' },
        { field: 'icms_cst', label: 'CST', type: 'text' },
        { field: 'icms_aliquota', label: 'Alíquota ICMS', type: 'percentage' },
        {
          field: 'icms_p_red_bc',
          label: 'Redução BC ICMS',
          type: 'percentage',
        },
        { field: 'icms_st_p_mva', label: 'MVA', type: 'percentage' },
        {
          field: 'icms_st_aliquota',
          label: 'Alíquota ICMS-ST',
          type: 'percentage',
        },
        {
          field: 'icms_st_vbc',
          label: 'Base de Cálculo ICMS-ST',
          type: 'currency',
        },
        { field: 'icms_st_valor', label: 'Valor ICMS-ST', type: 'currency' },
      ];
      break;
    case 'pis':
      specificFields = [
        { field: 'pis_cst', label: 'CST', type: 'text' },
        { field: 'pis_aliquota', label: 'Alíquota PIS', type: 'percentage' },
        { field: 'pis_vbc', label: 'Base de Cálculo PIS', type: 'currency' },
        { field: 'pis_valor', label: 'Valor PIS', type: 'currency' },
        { field: 'cliente.razao_social', label: 'Cliente', type: 'text' },
      ];
      break;
    case 'cofins':
      specificFields = [
        { field: 'cofins_cst', label: 'CST', type: 'text' },
        {
          field: 'cofins_aliquota',
          label: 'Alíquota COFINS',
          type: 'percentage',
        },
        {
          field: 'cofins_vbc',
          label: 'Base de Cálculo COFINS',
          type: 'currency',
        },
        { field: 'cofins_valor', label: 'Valor COFINS', type: 'currency' },
        { field: 'cliente.razao_social', label: 'Cliente', type: 'text' },
      ];
      break;
    case 'ipi':
      specificFields = [
        { field: 'ipi_cst', label: 'CST', type: 'text' },
        {
          field: 'ipi_codigo_enquadramento',
          label: 'Código Enquadramento',
          type: 'text',
        },
        { field: 'ipi_aliquota', label: 'Alíquota IPI', type: 'percentage' },
        { field: 'ipi_vbc', label: 'Base de Cálculo IPI', type: 'currency' },
        { field: 'ipi_valor', label: 'Valor IPI', type: 'currency' },
        { field: 'cliente.razao_social', label: 'Cliente', type: 'text' },
      ];
      break;
    case 'difal':
      specificFields = [
        {
          field: 'difal_vbc',
          label: 'Base de Cálculo DIFAL',
          type: 'currency',
        },
        {
          field: 'difal_p_fcp_uf_dest',
          label: 'FCP UF Destino',
          type: 'percentage',
        },
        {
          field: 'difal_p_icms_uf_dest',
          label: 'ICMS UF Destino',
          type: 'percentage',
        },
        {
          field: 'difal_p_icms_inter',
          label: 'ICMS Interestadual',
          type: 'percentage',
        },
        {
          field: 'difal_v_fcp_uf_dest',
          label: 'Valor FCP UF Destino',
          type: 'currency',
        },
        {
          field: 'difal_v_icms_uf_dest',
          label: 'Valor ICMS UF Destino',
          type: 'currency',
        },
        {
          field: 'difal_v_icms_uf_remet',
          label: 'Valor ICMS UF Remetente',
          type: 'currency',
        },
        { field: 'cliente.razao_social', label: 'Cliente', type: 'text' },
      ];
      break;
    default:
      specificFields = [];
  }

  return [...commonFields, ...specificFields];
}

/**
 * Obtém o valor de um campo de um objeto, suportando notação de ponto
 * @param {Object} obj - Objeto
 * @param {string|Object} field - Campo (pode usar notação de ponto, ex: 'cliente.razao_social') ou objeto com propriedade field
 * @returns {*} Valor do campo
 */
function getFieldValue(obj, field) {
  if (!obj) return null;

  // Verificar se field é uma string ou um objeto com propriedade field
  const fieldPath = typeof field === 'string' ? field : field.field;

  if (!fieldPath) return null;

  const path = fieldPath.split('.');
  let value = obj;

  for (const key of path) {
    if (value === null || value === undefined || !value.hasOwnProperty(key)) {
      return null;
    }
    value = value[key];
  }

  return value;
}

/**
 * Formata o valor de um campo com base no tipo
 * @param {*} value - Valor a ser formatado
 * @param {string} type - Tipo do campo (text, currency, percentage)
 * @returns {string} Valor formatado
 */
function formatFieldValue(value, type) {
  if (value === null || value === undefined) return 'N/A';

  switch (type) {
    case 'currency':
      return formatCurrency(value);
    case 'percentage':
      // Verificar se o valor já é uma string formatada
      if (typeof value === 'string' && value.includes('%')) {
        return value;
      }
      return formatPercentage(value);
    case 'text':
    default:
      return value;
  }
}

/**
 * Obtém o HTML com os detalhes específicos do tributo
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML com os detalhes do tributo
 */
function getTributoDetailsHTML(tributo) {
  // Adicionar o status do tributo específico
  const statusHTML = `
    <li class="list-group-item list-group-item-info">
      <strong>Status:</strong> ${
        tributo[`${window.cenariosDetalhes.currentTipoTributo}_status`] || 'N/A'
      }
    </li>
  `;

  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      return `
        ${statusHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          tributo.icms_cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Origem:</strong> ${
          tributo.icms_origem || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Base de Cálculo:</strong> ${formatCurrency(
          tributo.icms_vbc,
        )}</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${formatPercentage(
          tributo.icms_aliquota,
        )}</li>
        <li class="list-group-item"><strong>Redução BC:</strong> ${formatPercentage(
          tributo.icms_p_red_bc,
        )}</li>
        <li class="list-group-item"><strong>Valor:</strong> ${formatCurrency(
          tributo.icms_valor,
        )}</li>
        <li class="list-group-item"><strong>Valor Operação:</strong> ${formatCurrency(
          tributo.icms_v_op,
        )}</li>
      `;
    case 'icms_st':
      return `
        ${statusHTML}
        <li class="list-group-item"><strong>Modalidade BC:</strong> ${
          tributo.icms_st_mod_bc || 'N/A'
        }</li>
        <li class="list-group-item"><strong>MVA (%):</strong> ${formatPercentage(
          tributo.icms_st_p_mva,
        )}</li>
        <li class="list-group-item"><strong>Base de Cálculo:</strong> ${formatCurrency(
          tributo.icms_st_vbc,
        )}</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${formatPercentage(
          tributo.icms_st_aliquota,
        )}</li>
        <li class="list-group-item"><strong>Valor:</strong> ${formatCurrency(
          tributo.icms_st_valor,
        )}</li>
        <li class="list-group-item"><strong>ICMS Origem:</strong> ${
          tributo.icms_origem || 'N/A'
        }</li>
        <li class="list-group-item"><strong>ICMS CST:</strong> ${
          tributo.icms_cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>ICMS Alíquota:</strong> ${formatPercentage(
          tributo.icms_aliquota,
        )}</li>
        <li class="list-group-item"><strong>ICMS Redução BC:</strong> ${formatPercentage(
          tributo.icms_p_red_bc,
        )}</li>
      `;
    case 'ipi':
      return `
        ${statusHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          tributo.ipi_cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Código Enquadramento:</strong> ${
          tributo.ipi_codigo_enquadramento || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Base de Cálculo:</strong> ${formatCurrency(
          tributo.ipi_vbc,
        )}</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${formatPercentage(
          tributo.ipi_aliquota,
        )}</li>
        <li class="list-group-item"><strong>Valor:</strong> ${formatCurrency(
          tributo.ipi_valor,
        )}</li>
      `;
    case 'pis':
      return `
        ${statusHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          tributo.pis_cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Base de Cálculo:</strong> ${formatCurrency(
          tributo.pis_vbc,
        )}</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${formatPercentage(
          tributo.pis_aliquota,
        )}</li>
        <li class="list-group-item"><strong>Valor:</strong> ${formatCurrency(
          tributo.pis_valor,
        )}</li>
      `;
    case 'cofins':
      return `
        ${statusHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          tributo.cofins_cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Base de Cálculo:</strong> ${formatCurrency(
          tributo.cofins_vbc,
        )}</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${formatPercentage(
          tributo.cofins_aliquota,
        )}</li>
        <li class="list-group-item"><strong>Valor:</strong> ${formatCurrency(
          tributo.cofins_valor,
        )}</li>
      `;
    case 'difal':
      return `
        ${statusHTML}
        <li class="list-group-item"><strong>Base de Cálculo:</strong> ${formatCurrency(
          tributo.difal_vbc,
        )}</li>
        <li class="list-group-item"><strong>FCP UF Destino (%):</strong> ${formatPercentage(
          tributo.difal_p_fcp_uf_dest,
        )}</li>
        <li class="list-group-item"><strong>ICMS UF Destino (%):</strong> ${formatPercentage(
          tributo.difal_p_icms_uf_dest,
        )}</li>
        <li class="list-group-item"><strong>ICMS Interestadual (%):</strong> ${formatPercentage(
          tributo.difal_p_icms_inter,
        )}</li>
        <li class="list-group-item"><strong>Valor FCP UF Destino:</strong> ${formatCurrency(
          tributo.difal_v_fcp_uf_dest,
        )}</li>
        <li class="list-group-item"><strong>Valor ICMS UF Destino:</strong> ${formatCurrency(
          tributo.difal_v_icms_uf_dest,
        )}</li>
        <li class="list-group-item"><strong>Valor ICMS UF Remetente:</strong> ${formatCurrency(
          tributo.difal_v_icms_uf_remet,
        )}</li>
      `;
    default:
      return '<li class="list-group-item">Informações não disponíveis</li>';
  }
}

/**
 * Obtém o valor do tributo com base no tipo
 * @param {Object} tributo - Dados do tributo
 * @returns {number} Valor do tributo
 */
function getTributoValue(tributo) {
  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      return tributo.icms_valor;
    case 'icms_st':
      return tributo.icms_st_valor;
    case 'ipi':
      return tributo.ipi_valor;
    case 'pis':
      return tributo.pis_valor;
    case 'cofins':
      return tributo.cofins_valor;
    case 'difal':
      return tributo.difal_v_icms_uf_dest;
    default:
      return 0;
  }
}

/**
 * Obtém o título do tributo
 * @returns {string} Título do tributo
 */
function getTituloTributo() {
  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      return 'ICMS';
    case 'icms_st':
      return 'ICMS-ST';
    case 'ipi':
      return 'IPI';
    case 'pis':
      return 'PIS';
    case 'cofins':
      return 'COFINS';
    case 'difal':
      return 'DIFAL';
    default:
      return 'Tributo';
  }
}

/**
 * Formata um valor monetário
 * @param {number} value - Valor a ser formatado
 * @returns {string} Valor formatado
 */
function formatCurrency(value) {
  if (value === null || value === undefined) return 'N/A';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Formata um valor percentual
 * @param {number} value - Valor a ser formatado
 * @returns {string} Valor formatado
 */
function formatPercentage(value) {
  if (value === null || value === undefined) return 'N/A';
  return new Intl.NumberFormat('pt-BR', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 4,
  }).format(value / 100);
}

/**
 * Formata uma data
 * @param {string} dateString - String de data no formato ISO
 * @returns {string} Data formatada
 */
function formatDate(dateString) {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('pt-BR').format(date);
}

/**
 * Obtém os cabeçalhos da tabela com base no tipo de tributo
 * @returns {string} HTML dos cabeçalhos da tabela
 */
function getTributoTableHeaders() {
  // Colunas comuns para todos os tipos de tributo
  let headers = `
    <th>Produto</th>
    <th>Descrição</th>
    <th>NCM</th>
    <th>CFOP</th>
  `;

  // Colunas específicas para cada tipo de tributo
  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      headers += `
        <th>Origem</th>
        <th>CST</th>
        <th>% ICMS</th>
        <th>% Red. BC ICMS</th>
      `;
      break;
    case 'icms_st':
      headers += `
        <th>Origem</th>
        <th>CST</th>
        <th>% ICMS</th>
        <th>% Red. BC ICMS</th>
        <th>% MVA</th>
        <th>% ICMS ST</th>
        <th>% Red. BC ICMS ST</th>
      `;
      break;
    case 'pis':
    case 'cofins':
      headers += `
        <th>CST</th>
        <th>% ${window.cenariosDetalhes.currentTipoTributo.toUpperCase()}</th>
        <th>% Red. ${window.cenariosDetalhes.currentTipoTributo.toUpperCase()}</th>
        <th>Cliente</th>
      `;
      break;
    case 'ipi':
      headers += `
        <th>CST</th>
        <th>% IPI</th>
        <th>% Red. IPI</th>
        <th>Cliente</th>
      `;
      break;
    case 'difal':
      headers += `
        <th>% DIFAL ICMS</th>
        <th>% FCP</th>
        <th>Cliente</th>
      `;
      break;
    default:
      headers += `
        <th>Tipo</th>
        <th>Valor</th>
      `;
  }

  return headers;
}

/**
 * Obtém as linhas da tabela com base no tipo de tributo
 * @param {Array} tributos - Lista de tributos
 * @param {string} status - Status dos tributos
 * @returns {string} HTML das linhas da tabela
 */
function getTributoTableRows(tributos, status) {
  return tributos
    .map((tributo) => {
      // Colunas comuns para todos os tipos de tributo
      let row = `
        <tr data-tributo-id="${tributo.id}">
          <td>${tributo.produto?.codigo || 'N/A'}</td>
          <td>${tributo.produto?.descricao || 'N/A'}</td>
          <td>${tributo.produto?.ncm || 'N/A'}</td>
          <td>${tributo.produto?.cfop || 'N/A'}</td>
      `;

      // Colunas específicas para cada tipo de tributo
      switch (window.cenariosDetalhes.currentTipoTributo) {
        case 'icms':
          row += `
            <td>${tributo.icms_origem || 'N/A'}</td>
            <td>${tributo.icms_cst || 'N/A'}</td>
            <td>${formatPercentage(tributo.icms_aliquota)}</td>
            <td>${formatPercentage(tributo.icms_p_red_bc)}</td>
          `;
          break;
        case 'icms_st':
          row += `
            <td>${tributo.icms_origem || 'N/A'}</td>
            <td>${tributo.icms_cst || 'N/A'}</td>
            <td>${formatPercentage(tributo.icms_aliquota)}</td>
            <td>${formatPercentage(tributo.icms_p_red_bc)}</td>
            <td>${formatPercentage(tributo.icms_st_p_mva)}</td>
            <td>${formatPercentage(tributo.icms_st_aliquota)}</td>
            <td>N/A</td> <!-- % Red. BC ICMS ST - Não encontrado no modelo -->
          `;
          break;
        case 'pis':
          row += `
            <td>${tributo.pis_cst || 'N/A'}</td>
            <td>${formatPercentage(tributo.pis_aliquota)}</td>
            <td>N/A</td> <!-- % Red. PIS - Não encontrado no modelo -->
            <td>${tributo.cliente?.razao_social || 'N/A'}</td>
          `;
          break;
        case 'cofins':
          row += `
            <td>${tributo.cofins_cst || 'N/A'}</td>
            <td>${formatPercentage(tributo.cofins_aliquota)}</td>
            <td>N/A</td> <!-- % Red. COFINS - Não encontrado no modelo -->
            <td>${tributo.cliente?.razao_social || 'N/A'}</td>
          `;
          break;
        case 'ipi':
          row += `
            <td>${tributo.ipi_cst || 'N/A'}</td>
            <td>${formatPercentage(tributo.ipi_aliquota)}</td>
            <td>N/A</td> <!-- % Red. IPI - Não encontrado no modelo -->
            <td>${tributo.cliente?.razao_social || 'N/A'}</td>
          `;
          break;
        case 'difal':
          row += `
            <td>${formatPercentage(tributo.difal_p_icms_uf_dest)}</td>
            <td>${formatPercentage(tributo.difal_p_fcp_uf_dest)}</td>
            <td>${tributo.cliente?.razao_social || 'N/A'}</td>
          `;
          break;
        default:
          row += `
            <td>${
              tributo.tipo_operacao === '0'
                ? 'Entrada'
                : tributo.tipo_operacao === '1'
                ? 'Saída'
                : 'N/A'
            }</td>
            <td>${formatCurrency(getTributoValue(tributo))}</td>
          `;
      }

      // Coluna de ações
      row += `
          <td>
            <button class="btn btn-sm btn-primary edit-tributo-btn" data-tributo-id="${
              tributo.id
            }" title="Editar tributo">
              <i class="fas fa-edit"></i>
            </button>
            ${
              status === 'novo' || status === 'inconsistente'
                ? `
              <button class="btn btn-sm btn-success send-to-producao-btn" data-tributo-id="${tributo.id}" title="Enviar para produção">
                <i class="fas fa-check"></i>
              </button>
            `
                : ''
            }
          </td>
        </tr>
      `;

      return row;
    })
    .join('');
}

/**
 * Atualiza o status de um cenário
 * @param {number} cenarioId - ID do cenário
 * @param {string} novoStatus - Novo status ('novo', 'producao', 'inconsistente')
 */
function updateCenarioStatus(cenarioId, novoStatus) {
  console.log(
    `[DEBUG] Iniciando updateCenarioStatus - ID: ${cenarioId}, Status: ${novoStatus}`,
  );

  // Verificar se o cenário atual está definido
  if (!cenarioId) {
    console.error('ID do cenário não fornecido');
    return Promise.reject('ID do cenário não fornecido');
  }

  // Obter o ID da empresa e do escritório
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = parseInt(currentUser.escritorio_id || '0');

  // Verificar se os IDs são válidos
  if (isNaN(empresaId) || empresaId <= 0) {
    alert('ID da empresa inválido. Por favor, selecione uma empresa válida.');
    return Promise.reject('ID da empresa inválido');
  }

  if (isNaN(escritorioId) || escritorioId <= 0) {
    alert('ID do escritório não encontrado. Por favor, faça login novamente.');
    return Promise.reject('ID do escritório inválido');
  }

  // Dados a serem enviados
  const data = {
    empresa_id: empresaId,
    escritorio_id: escritorioId,
    status: novoStatus,
  };

  // Função para enviar a requisição de atualização
  function sendUpdateRequest() {
    console.log(
      `[DEBUG] Enviando requisição para /api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioId}/status`,
      data,
    );

    return fetch(
      `/api/cenarios/${window.cenariosDetalhes.currentTipoTributo}/${cenarioId}/status`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(data),
      },
    )
      .then((response) => response.json())
      .then((responseData) => {
        if (responseData.success) {
          alert(`Status do cenário atualizado para ${novoStatus} com sucesso!`);

          // Recarregar os dados para todas as abas
          loadCenarioData('novo');
          loadCenarioData('producao');
          loadCenarioData('inconsistente');
        } else {
          alert(
            responseData.message ||
              `Erro ao atualizar status do cenário para ${novoStatus}.`,
          );
        }
        return responseData;
      })
      .catch((error) => {
        console.error('Erro ao atualizar status do cenário:', error);
        alert(`Erro ao atualizar status do cenário para ${novoStatus}.`);
        throw error;
      });
  }

  // Se for produção, já envia a requisição (o backend vai buscar a data)
  if (novoStatus === 'producao') {
    console.log(
      '[DEBUG] Enviando para produção. O backend irá buscar a data de emissão.',
    );
  }

  // Envia a requisição
  return sendUpdateRequest().catch((error) => {
    console.error(
      `Erro ao atualizar status do cenário para ${novoStatus}:`,
      error,
    );
  });
}

/**
 * Exclui um cenário
 * @param {number} cenarioId - ID do cenário
 */
function excluirCenario(cenarioId) {
  // Confirmar exclusão
  if (
    !confirm(
      'Tem certeza que deseja excluir este cenário? Esta ação não pode ser desfeita.',
    )
  ) {
    return;
  }

  // Verificar se a empresa está selecionada
  if (!window.cenariosDetalhes.selectedCompany) {
    alert('Por favor, selecione uma empresa antes de excluir o cenário.');
    return;
  }

  // Obter o ID da empresa e do escritório
  const empresaId = parseInt(window.cenariosDetalhes.selectedCompany);

  // Obter o escritório ID do objeto currentUser no localStorage
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  const escritorioId = parseInt(currentUser.escritorio_id || '0');

  // Verificar se os IDs são válidos
  if (isNaN(empresaId) || empresaId <= 0) {
    alert('ID da empresa inválido. Por favor, selecione uma empresa válida.');
    return;
  }

  if (isNaN(escritorioId) || escritorioId <= 0) {
    alert('ID do escritório não encontrado. Por favor, faça login novamente.');
    return;
  }

  // Preparar parâmetros para a requisição
  const params = new URLSearchParams();
  params.append('empresa_id', empresaId);
  params.append('escritorio_id', escritorioId);

  // Fazer requisição para excluir o cenário
  fetch(
    `/api/cenarios/${
      window.cenariosDetalhes.currentTipoTributo
    }/${cenarioId}?${params.toString()}`,
    {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    },
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert('Cenário excluído com sucesso!');

        // Recarregar os dados para todas as abas
        loadCenarioData('novo');
        loadCenarioData('producao');
        loadCenarioData('inconsistente');
      } else {
        alert(data.message || 'Erro ao excluir cenário.');
      }
    })
    .catch((error) => {
      alert('Erro ao excluir cenário.');
    });
}

/**
 * Configura as scrollbars duplas (superior e inferior) para a tabela
 * @param {string} status - Status dos cenários
 */
function setupDualScrollbars(status) {
  const topScrollbar = document.getElementById(`table-top-scrollbar-${status}`);
  const topScrollbarContent = document.getElementById(
    `table-top-scrollbar-content-${status}`,
  );
  const tableResponsive = document.getElementById(`table-responsive-${status}`);
  const table = document.getElementById(`cenario-${status}-table`);

  if (!topScrollbar || !topScrollbarContent || !tableResponsive || !table) {
    console.warn(
      `Elementos de scrollbar não encontrados para status ${status}`,
    );
    return;
  }

  // Função para atualizar a largura da scrollbar superior
  function updateTopScrollbarWidth() {
    const tableWidth = table.scrollWidth;
    const containerWidth = tableResponsive.clientWidth;

    // Só mostrar a scrollbar superior se a tabela for maior que o container
    if (tableWidth > containerWidth) {
      topScrollbar.style.display = 'block';
      topScrollbarContent.style.width = `${tableWidth}px`;
    } else {
      topScrollbar.style.display = 'none';
    }
  }

  // Função para sincronizar o scroll da scrollbar superior com a tabela
  function syncTopScrollbar() {
    if (topScrollbar.scrollLeft !== tableResponsive.scrollLeft) {
      topScrollbar.scrollLeft = tableResponsive.scrollLeft;
    }
  }

  // Função para sincronizar o scroll da tabela com a scrollbar superior
  function syncTableScroll() {
    if (tableResponsive.scrollLeft !== topScrollbar.scrollLeft) {
      tableResponsive.scrollLeft = topScrollbar.scrollLeft;
    }
  }

  // Event listeners para sincronização
  tableResponsive.addEventListener('scroll', syncTopScrollbar);
  topScrollbar.addEventListener('scroll', syncTableScroll);

  // Atualizar largura inicial
  updateTopScrollbarWidth();

  // Observar mudanças na tabela para atualizar a largura da scrollbar
  const resizeObserver = new ResizeObserver(() => {
    updateTopScrollbarWidth();
  });

  resizeObserver.observe(table);
  resizeObserver.observe(tableResponsive);

  // Atualizar quando colunas são mostradas/ocultadas
  if (window.cenariosDetalhes.cenarioTable) {
    window.cenariosDetalhes.cenarioTable.on('column-visibility.dt', () => {
      setTimeout(updateTopScrollbarWidth, 100);
    });

    window.cenariosDetalhes.cenarioTable.on('draw.dt', () => {
      setTimeout(updateTopScrollbarWidth, 100);
    });
  }

  // Armazenar referências para limpeza posterior se necessário
  if (!window.cenariosDetalhes.scrollbarCleanup) {
    window.cenariosDetalhes.scrollbarCleanup = {};
  }

  window.cenariosDetalhes.scrollbarCleanup[status] = {
    resizeObserver,
    topScrollbar,
    tableResponsive,
    syncTopScrollbar,
    syncTableScroll,
  };

  console.log(`Scrollbars duplas configuradas para status ${status}`);
}

/**
 * Limpa as configurações de scrollbars duplas para um status específico
 * @param {string} status - Status dos cenários
 */
function cleanupDualScrollbars(status) {
  if (
    window.cenariosDetalhes.scrollbarCleanup &&
    window.cenariosDetalhes.scrollbarCleanup[status]
  ) {
    const cleanup = window.cenariosDetalhes.scrollbarCleanup[status];

    // Remover event listeners
    if (cleanup.tableResponsive && cleanup.syncTopScrollbar) {
      cleanup.tableResponsive.removeEventListener(
        'scroll',
        cleanup.syncTopScrollbar,
      );
    }

    if (cleanup.topScrollbar && cleanup.syncTableScroll) {
      cleanup.topScrollbar.removeEventListener(
        'scroll',
        cleanup.syncTableScroll,
      );
    }

    // Desconectar ResizeObserver
    if (cleanup.resizeObserver) {
      cleanup.resizeObserver.disconnect();
    }

    // Remover da lista de cleanup
    delete window.cenariosDetalhes.scrollbarCleanup[status];

    console.log(`Scrollbars duplas limpas para status ${status}`);
  }
}
