from .escritorio import db
from sqlalchemy.sql import func

class AuditoriaResultado(db.Model):
    __tablename__ = 'auditoria_resultado'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.<PERSON>umn(db.<PERSON>, db.<PERSON>('escritorio.id'), nullable=False)
    tributo_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON>ey('tributo.id'), nullable=False)
    nota_fiscal_item_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('nota_fiscal_item.id'), nullable=False)
    tipo_tributo = db.Column(db.String(20), nullable=False)  # 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'
    cenario_id = db.Column(db.Integer, nullable=False)  # ID do cenário utilizado
    valor_nota = db.<PERSON>umn(db.<PERSON>ume<PERSON>(15, 2))  # Valor do tributo na nota
    valor_calculado = db.Column(db.Numeric(15, 2))  # Valor calculado com base no cenário
    base_calculo_nota = db.Column(db.Numeric(15, 2))  # Base de cálculo na nota
    base_calculo_calculada = db.Column(db.Numeric(15, 2))  # Base de cálculo calculada

    # Campos para comparação de dados fiscais
    cst_nota = db.Column(db.String(3))  # CST da nota fiscal
    cst_cenario = db.Column(db.String(3))  # CST do cenário
    origem_nota = db.Column(db.String(2))  # Origem da nota fiscal
    origem_cenario = db.Column(db.String(2))  # Origem do cenário
    aliquota_nota = db.Column(db.Numeric(10, 4))  # Alíquota da nota fiscal
    aliquota_cenario = db.Column(db.Numeric(10, 4))  # Alíquota do cenário

    # Campos para indicar tipos de inconsistências
    inconsistencia_valor = db.Column(db.Boolean, default=False)  # Inconsistência no valor
    inconsistencia_cst = db.Column(db.Boolean, default=False)  # Inconsistência no CST
    inconsistencia_origem = db.Column(db.Boolean, default=False)  # Inconsistência na origem
    inconsistencia_aliquota = db.Column(db.Boolean, default=False)  # Inconsistência na alíquota
    inconsistencia_base_calculo = db.Column(db.Boolean, default=False)  # Inconsistência na base de cálculo

    status = db.Column(db.String(20), nullable=False)  # 'conforme', 'inconsistente'
    data_auditoria = db.Column(db.DateTime, server_default=func.now())

    # Campos para análise do analista
    analista_visualizou = db.Column(db.Boolean, default=False)
    observacoes_analista = db.Column(db.Text)
    data_visualizacao = db.Column(db.DateTime)
    usuario_analista_id = db.Column(db.Integer, db.ForeignKey('usuario.id'))

    # Relacionamentos
    empresa = db.relationship('Empresa', backref='auditoria_resultados')
    escritorio = db.relationship('Escritorio', backref='auditoria_resultados')
    tributo = db.relationship('Tributo', backref='auditoria_resultados')
    nota_fiscal_item = db.relationship('NotaFiscalItem', backref='auditoria_resultados')
    usuario_analista = db.relationship('Usuario', backref='auditoria_resultados_analisados')

    def __repr__(self):
        return f"<AuditoriaResultado {self.id} - Tributo {self.tributo_id} - {self.tipo_tributo}>"

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'tributo_id': self.tributo_id,
            'nota_fiscal_item_id': self.nota_fiscal_item_id,
            'tipo_tributo': self.tipo_tributo,
            'cenario_id': self.cenario_id,
            'valor_nota': float(self.valor_nota) if self.valor_nota else None,
            'valor_calculado': float(self.valor_calculado) if self.valor_calculado else None,
            'base_calculo_nota': float(self.base_calculo_nota) if self.base_calculo_nota else None,
            'base_calculo_calculada': float(self.base_calculo_calculada) if self.base_calculo_calculada else None,

            # Campos de comparação fiscal
            'cst_nota': self.cst_nota,
            'cst_cenario': self.cst_cenario,
            'origem_nota': self.origem_nota,
            'origem_cenario': self.origem_cenario,
            'aliquota_nota': float(self.aliquota_nota) if self.aliquota_nota else None,
            'aliquota_cenario': float(self.aliquota_cenario) if self.aliquota_cenario else None,

            # Tipos de inconsistências
            'inconsistencia_valor': self.inconsistencia_valor,
            'inconsistencia_cst': self.inconsistencia_cst,
            'inconsistencia_origem': self.inconsistencia_origem,
            'inconsistencia_aliquota': self.inconsistencia_aliquota,
            'inconsistencia_base_calculo': self.inconsistencia_base_calculo,

            'status': self.status,
            'data_auditoria': self.data_auditoria.isoformat() if self.data_auditoria else None,
            'analista_visualizou': self.analista_visualizou,
            'observacoes_analista': self.observacoes_analista,
            'data_visualizacao': self.data_visualizacao.isoformat() if self.data_visualizacao else None,
            'usuario_analista_id': self.usuario_analista_id
        }
