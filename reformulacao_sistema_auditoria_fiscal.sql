-- Script SQL para reformulação do Sistema de Auditoria Fiscal

-- 1. Novas Tabelas de Cenários

-- Cenário ICMS
CREATE TABLE IF NOT EXISTS cenario_icms (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do ICMS
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc DECIMAL(10, 4),
    aliquota DECIMAL(10, 4),
    p_dif DECIMAL(10, 4),

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente'
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    -- Restrição para evitar cenários duplicados
    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_icms_empresa ON cenario_icms(empresa_id);
CREATE INDEX idx_cenario_icms_cliente ON cenario_icms(cliente_id);
CREATE INDEX idx_cenario_icms_produto ON cenario_icms(produto_id);
CREATE INDEX idx_cenario_icms_status ON cenario_icms(status);
CREATE INDEX idx_cenario_icms_ativo ON cenario_icms(ativo);

-- Cenário ICMS-ST
CREATE TABLE IF NOT EXISTS cenario_icms_st (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do ICMS-ST
    -- Campos de ICMS que também são usados no ICMS-ST
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc DECIMAL(10, 4),
    aliquota DECIMAL(10, 4),

    -- Campos específicos do ICMS-ST
    icms_st_mod_bc VARCHAR(2),
    icms_st_aliquota DECIMAL(10, 4),
    icms_st_p_mva DECIMAL(10, 4),

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_icms_st_empresa ON cenario_icms_st(empresa_id);
CREATE INDEX idx_cenario_icms_st_cliente ON cenario_icms_st(cliente_id);
CREATE INDEX idx_cenario_icms_st_produto ON cenario_icms_st(produto_id);
CREATE INDEX idx_cenario_icms_st_status ON cenario_icms_st(status);
CREATE INDEX idx_cenario_icms_st_ativo ON cenario_icms_st(ativo);

-- Cenário IPI
CREATE TABLE IF NOT EXISTS cenario_ipi (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do IPI
    cst VARCHAR(3),
    aliquota DECIMAL(10, 4),
    ex VARCHAR(3),  -- Novo campo para EXTIPI

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),
    
    -- Restrição de unicidade que não inclui o status 'producao'
    CONSTRAINT uq_cenario_ipi_unique EXCLUDE USING btree (
        empresa_id WITH =,
        cliente_id WITH =,
        produto_id WITH =,
        status WITH =
    ) WHERE (status != 'producao')
);

CREATE INDEX idx_cenario_ipi_empresa ON cenario_ipi(empresa_id);
CREATE INDEX idx_cenario_ipi_cliente ON cenario_ipi(cliente_id);
CREATE INDEX idx_cenario_ipi_produto ON cenario_ipi(produto_id);
CREATE INDEX idx_cenario_ipi_status ON cenario_ipi(status);
CREATE INDEX idx_cenario_ipi_ativo ON cenario_ipi(ativo);

-- Cenário PIS
CREATE TABLE IF NOT EXISTS cenario_pis (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do PIS
    cst VARCHAR(3),
    aliquota DECIMAL(10, 4),
    p_red_bc DECIMAL(10, 4),  -- Novo campo para percentual de redução da base de cálculo

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_pis_empresa ON cenario_pis(empresa_id);
CREATE INDEX idx_cenario_pis_cliente ON cenario_pis(cliente_id);
CREATE INDEX idx_cenario_pis_produto ON cenario_pis(produto_id);
CREATE INDEX idx_cenario_pis_status ON cenario_pis(status);
CREATE INDEX idx_cenario_pis_ativo ON cenario_pis(ativo);

-- Cenário COFINS
CREATE TABLE IF NOT EXISTS cenario_cofins (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do COFINS
    cst VARCHAR(3),
    aliquota DECIMAL(10, 4),
    p_red_bc DECIMAL(10, 4),  -- Novo campo para percentual de redução da base de cálculo

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_cofins_empresa ON cenario_cofins(empresa_id);
CREATE INDEX idx_cenario_cofins_cliente ON cenario_cofins(cliente_id);
CREATE INDEX idx_cenario_cofins_produto ON cenario_cofins(produto_id);
CREATE INDEX idx_cenario_cofins_status ON cenario_cofins(status);
CREATE INDEX idx_cenario_cofins_ativo ON cenario_cofins(ativo);

-- Cenário DIFAL
CREATE TABLE IF NOT EXISTS cenario_difal (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos de ICMS que também são usados no DIFAL
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc DECIMAL(10, 4),
    aliquota DECIMAL(10, 4),

    -- Campos específicos do DIFAL
    p_fcp_uf_dest DECIMAL(10, 4),
    p_icms_uf_dest DECIMAL(10, 4),
    p_icms_inter DECIMAL(10, 4),
    p_icms_inter_part DECIMAL(10, 4),

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_difal_empresa ON cenario_difal(empresa_id);
CREATE INDEX idx_cenario_difal_cliente ON cenario_difal(cliente_id);
CREATE INDEX idx_cenario_difal_produto ON cenario_difal(produto_id);
CREATE INDEX idx_cenario_difal_status ON cenario_difal(status);
CREATE INDEX idx_cenario_difal_ativo ON cenario_difal(ativo);

-- 2. Modificações na Tabela de Tributos

-- Adicionar colunas para valores calculados
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS icms_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS icms_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS icms_st_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS icms_st_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS ipi_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS ipi_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS pis_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS pis_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cofins_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cofins_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS difal_calculado_valor DECIMAL(10, 2);

-- Nova coluna para IPI EX
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS ipi_ex VARCHAR(3);

-- Novas colunas para percentual de redução de base de cálculo para PIS e COFINS
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS pis_p_red_bc DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cofins_p_red_bc DECIMAL(10, 4);

-- 3. Modificações na Tabela de Clientes

-- Atualizar a coluna de status para refletir o preenchimento dos campos de atividade e destinação
-- Valores possíveis: 'ok', 'sem_atividade', 'sem_destinacao', 'sem_ambos'
ALTER TABLE cliente ALTER COLUMN status TYPE VARCHAR(20);

-- 4. Modificações na Tabela de Produtos

-- Simplificar o status do produto
-- Valores possíveis: 'conforme', 'nulo'
ALTER TABLE produto ALTER COLUMN status TYPE VARCHAR(20);

-- 5. Remover colunas de status específicos de tributos que não serão mais utilizadas

-- Primeiro, verificar se as colunas existem antes de tentar removê-las
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tributo' AND column_name = 'icms_status') THEN
        ALTER TABLE tributo DROP COLUMN icms_status;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tributo' AND column_name = 'icms_st_status') THEN
        ALTER TABLE tributo DROP COLUMN icms_st_status;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tributo' AND column_name = 'ipi_status') THEN
        ALTER TABLE tributo DROP COLUMN ipi_status;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tributo' AND column_name = 'pis_status') THEN
        ALTER TABLE tributo DROP COLUMN pis_status;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tributo' AND column_name = 'cofins_status') THEN
        ALTER TABLE tributo DROP COLUMN cofins_status;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tributo' AND column_name = 'difal_status') THEN
        ALTER TABLE tributo DROP COLUMN difal_status;
    END IF;
END $$;

-- 6. Funções para atualização de status e cálculos

-- Função para atualizar o status do cliente com base no preenchimento de atividade e destinação
CREATE OR REPLACE FUNCTION atualizar_status_cliente()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.atividade IS NULL AND NEW.destinacao IS NULL THEN
        NEW.status = 'sem_ambos';
    ELSIF NEW.atividade IS NULL THEN
        NEW.status = 'sem_atividade';
    ELSIF NEW.destinacao IS NULL THEN
        NEW.status = 'sem_destinacao';
    ELSE
        NEW.status = 'ok';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar o status do cliente automaticamente
DROP TRIGGER IF EXISTS trigger_atualizar_status_cliente ON cliente;
CREATE TRIGGER trigger_atualizar_status_cliente
BEFORE INSERT OR UPDATE OF atividade, destinacao ON cliente
FOR EACH ROW EXECUTE FUNCTION atualizar_status_cliente();

-- Função para calcular tributos com base nos cenários ativos
CREATE OR REPLACE FUNCTION calcular_tributos_cenario(p_tributo_id INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    v_tributo RECORD;
    v_cenario_icms RECORD;
    v_cenario_icms_st RECORD;
    v_cenario_ipi RECORD;
    v_cenario_pis RECORD;
    v_cenario_cofins RECORD;
    v_cenario_difal RECORD;
    v_base_calculo DECIMAL(10, 2);
    v_valor_calculado DECIMAL(10, 2);
    v_todos_calculados BOOLEAN := TRUE;
BEGIN
    -- Buscar o tributo
    SELECT * INTO v_tributo FROM tributo WHERE id = p_tributo_id;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Calcular ICMS
    SELECT * INTO v_cenario_icms FROM cenario_icms
    WHERE empresa_id = v_tributo.empresa_id
    AND cliente_id = v_tributo.cliente_id
    AND produto_id = v_tributo.produto_id
    AND status = 'producao'
    AND ativo = TRUE;

    IF FOUND AND v_cenario_icms.aliquota IS NOT NULL AND v_tributo.valor_total IS NOT NULL THEN
        -- Aplicar redução da base de cálculo, se houver
        v_base_calculo := v_tributo.valor_total;
        IF v_cenario_icms.p_red_bc IS NOT NULL AND v_cenario_icms.p_red_bc > 0 THEN
            v_base_calculo := v_base_calculo * (1 - v_cenario_icms.p_red_bc / 100);
        END IF;

        -- Calcular valor do ICMS
        v_valor_calculado := (v_base_calculo * v_cenario_icms.aliquota) / 100;

        -- Atualizar valores calculados no tributo
        UPDATE tributo SET
            icms_calculado_aliquota = v_cenario_icms.aliquota,
            icms_calculado_valor = v_valor_calculado
        WHERE id = p_tributo_id;
    ELSE
        v_todos_calculados := FALSE;
    END IF;

    -- Calcular ICMS-ST
    SELECT * INTO v_cenario_icms_st FROM cenario_icms_st
    WHERE empresa_id = v_tributo.empresa_id
    AND cliente_id = v_tributo.cliente_id
    AND produto_id = v_tributo.produto_id
    AND status = 'producao'
    AND ativo = TRUE;

    IF FOUND AND v_cenario_icms_st.icms_st_aliquota IS NOT NULL AND v_tributo.valor_total IS NOT NULL THEN
        -- Aplicar redução da base de cálculo, se houver
        v_base_calculo := v_tributo.valor_total;
        IF v_cenario_icms_st.p_red_bc IS NOT NULL AND v_cenario_icms_st.p_red_bc > 0 THEN
            v_base_calculo := v_base_calculo * (1 - v_cenario_icms_st.p_red_bc / 100);
        END IF;

        -- Aplicar MVA, se houver
        IF v_cenario_icms_st.icms_st_p_mva IS NOT NULL AND v_cenario_icms_st.icms_st_p_mva > 0 THEN
            v_base_calculo := v_base_calculo * (1 + v_cenario_icms_st.icms_st_p_mva / 100);
        END IF;

        -- Calcular valor do ICMS-ST
        v_valor_calculado := (v_base_calculo * v_cenario_icms_st.icms_st_aliquota) / 100;

        -- Subtrair o ICMS próprio, se houver
        IF v_tributo.icms_calculado_valor IS NOT NULL THEN
            v_valor_calculado := v_valor_calculado - v_tributo.icms_calculado_valor;
        END IF;

        -- Atualizar valores calculados no tributo
        UPDATE tributo SET
            icms_st_calculado_aliquota = v_cenario_icms_st.icms_st_aliquota,
            icms_st_calculado_valor = v_valor_calculado
        WHERE id = p_tributo_id;
    ELSE
        v_todos_calculados := FALSE;
    END IF;

    -- Calcular IPI
    SELECT * INTO v_cenario_ipi FROM cenario_ipi
    WHERE empresa_id = v_tributo.empresa_id
    AND cliente_id = v_tributo.cliente_id
    AND produto_id = v_tributo.produto_id
    AND status = 'producao'
    AND ativo = TRUE;

    IF FOUND AND v_cenario_ipi.aliquota IS NOT NULL AND v_tributo.valor_total IS NOT NULL THEN
        -- Calcular valor do IPI
        v_valor_calculado := (v_tributo.valor_total * v_cenario_ipi.aliquota) / 100;

        -- Atualizar valores calculados no tributo
        UPDATE tributo SET
            ipi_calculado_aliquota = v_cenario_ipi.aliquota,
            ipi_calculado_valor = v_valor_calculado,
            ipi_ex = v_cenario_ipi.ex
        WHERE id = p_tributo_id;
    ELSE
        v_todos_calculados := FALSE;
    END IF;

    -- Calcular PIS
    SELECT * INTO v_cenario_pis FROM cenario_pis
    WHERE empresa_id = v_tributo.empresa_id
    AND cliente_id = v_tributo.cliente_id
    AND produto_id = v_tributo.produto_id
    AND status = 'producao'
    AND ativo = TRUE;

    IF FOUND AND v_cenario_pis.aliquota IS NOT NULL AND v_tributo.valor_total IS NOT NULL THEN
        -- Aplicar redução da base de cálculo, se houver
        v_base_calculo := v_tributo.valor_total;
        IF v_cenario_pis.p_red_bc IS NOT NULL AND v_cenario_pis.p_red_bc > 0 THEN
            v_base_calculo := v_base_calculo * (1 - v_cenario_pis.p_red_bc / 100);
        END IF;

        -- Calcular valor do PIS
        v_valor_calculado := (v_base_calculo * v_cenario_pis.aliquota) / 100;

        -- Atualizar valores calculados no tributo
        UPDATE tributo SET
            pis_calculado_aliquota = v_cenario_pis.aliquota,
            pis_calculado_valor = v_valor_calculado,
            pis_p_red_bc = v_cenario_pis.p_red_bc
        WHERE id = p_tributo_id;
    ELSE
        v_todos_calculados := FALSE;
    END IF;

    -- Calcular COFINS
    SELECT * INTO v_cenario_cofins FROM cenario_cofins
    WHERE empresa_id = v_tributo.empresa_id
    AND cliente_id = v_tributo.cliente_id
    AND produto_id = v_tributo.produto_id
    AND status = 'producao'
    AND ativo = TRUE;

    IF FOUND AND v_cenario_cofins.aliquota IS NOT NULL AND v_tributo.valor_total IS NOT NULL THEN
        -- Aplicar redução da base de cálculo, se houver
        v_base_calculo := v_tributo.valor_total;
        IF v_cenario_cofins.p_red_bc IS NOT NULL AND v_cenario_cofins.p_red_bc > 0 THEN
            v_base_calculo := v_base_calculo * (1 - v_cenario_cofins.p_red_bc / 100);
        END IF;

        -- Calcular valor do COFINS
        v_valor_calculado := (v_base_calculo * v_cenario_cofins.aliquota) / 100;

        -- Atualizar valores calculados no tributo
        UPDATE tributo SET
            cofins_calculado_aliquota = v_cenario_cofins.aliquota,
            cofins_calculado_valor = v_valor_calculado,
            cofins_p_red_bc = v_cenario_cofins.p_red_bc
        WHERE id = p_tributo_id;
    ELSE
        v_todos_calculados := FALSE;
    END IF;

    -- Calcular DIFAL
    SELECT * INTO v_cenario_difal FROM cenario_difal
    WHERE empresa_id = v_tributo.empresa_id
    AND cliente_id = v_tributo.cliente_id
    AND produto_id = v_tributo.produto_id
    AND status = 'producao'
    AND ativo = TRUE;

    IF FOUND AND v_cenario_difal.p_icms_uf_dest IS NOT NULL
       AND v_cenario_difal.p_icms_inter IS NOT NULL
       AND v_tributo.valor_total IS NOT NULL THEN
        DECLARE
            v_diferenca DECIMAL(10, 4);
            v_fcp DECIMAL(10, 2);
        BEGIN
            -- Aplicar redução da base de cálculo, se houver
            v_base_calculo := v_tributo.valor_total;
            IF v_cenario_difal.p_red_bc IS NOT NULL AND v_cenario_difal.p_red_bc > 0 THEN
                v_base_calculo := v_base_calculo * (1 - v_cenario_difal.p_red_bc / 100);
            END IF;

            -- Calcular diferença entre alíquotas
            v_diferenca := v_cenario_difal.p_icms_uf_dest - v_cenario_difal.p_icms_inter;

            -- Aplicar percentual de partilha
            IF v_cenario_difal.p_icms_inter_part IS NOT NULL THEN
                v_diferenca := v_diferenca * v_cenario_difal.p_icms_inter_part / 100;
            END IF;

            -- Calcular valor do DIFAL
            v_valor_calculado := (v_base_calculo * v_diferenca) / 100;

            -- Calcular FCP, se houver
            IF v_cenario_difal.p_fcp_uf_dest IS NOT NULL AND v_cenario_difal.p_fcp_uf_dest > 0 THEN
                v_fcp := (v_base_calculo * v_cenario_difal.p_fcp_uf_dest) / 100;
                v_valor_calculado := v_valor_calculado + v_fcp;
            END IF;
        END;

        -- Atualizar valor calculado no tributo
        UPDATE tributo SET
            difal_calculado_valor = v_valor_calculado
        WHERE id = p_tributo_id;
    ELSE
        v_todos_calculados := FALSE;
    END IF;

    -- Se todos os tributos foram calculados, atualizar o status do produto para 'conforme'
    IF v_todos_calculados THEN
        UPDATE produto SET status = 'conforme' WHERE id = v_tributo.produto_id;
    ELSE
        UPDATE produto SET status = 'nulo' WHERE id = v_tributo.produto_id;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Função para atualizar o status de todos os tributos relacionados a um produto
CREATE OR REPLACE FUNCTION atualizar_status_tributos_produto(p_produto_id INTEGER, p_tipo_tributo VARCHAR, p_novo_status VARCHAR)
RETURNS VOID AS $$
DECLARE
    v_tributo RECORD;
BEGIN
    -- Percorrer todos os tributos do produto
    FOR v_tributo IN SELECT id FROM tributo WHERE produto_id = p_produto_id
    LOOP
        -- Verificar o tipo de tributo e atualizar o status correspondente
        IF p_tipo_tributo = 'icms' THEN
            UPDATE cenario_icms SET status = p_novo_status
            WHERE produto_id = p_produto_id AND status = 'novo';
        ELSIF p_tipo_tributo = 'icms_st' THEN
            UPDATE cenario_icms_st SET status = p_novo_status
            WHERE produto_id = p_produto_id AND status = 'novo';
        ELSIF p_tipo_tributo = 'ipi' THEN
            UPDATE cenario_ipi SET status = p_novo_status
            WHERE produto_id = p_produto_id AND status = 'novo';
        ELSIF p_tipo_tributo = 'pis' THEN
            UPDATE cenario_pis SET status = p_novo_status
            WHERE produto_id = p_produto_id AND status = 'novo';
        ELSIF p_tipo_tributo = 'cofins' THEN
            UPDATE cenario_cofins SET status = p_novo_status
            WHERE produto_id = p_produto_id AND status = 'novo';
        ELSIF p_tipo_tributo = 'difal' THEN
            UPDATE cenario_difal SET status = p_novo_status
            WHERE produto_id = p_produto_id AND status = 'novo';
        END IF;

        -- Recalcular os tributos
        PERFORM calcular_tributos_cenario(v_tributo.id);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Função para criar cenários durante a importação XML
CREATE OR REPLACE FUNCTION criar_cenario_importacao(
    p_empresa_id INTEGER,
    p_escritorio_id INTEGER,
    p_cliente_id INTEGER,
    p_produto_id INTEGER,
    p_tipo_tributo VARCHAR,
    p_tributo_data JSONB
) RETURNS INTEGER AS $$
DECLARE
    v_cenario_id INTEGER;
    v_status VARCHAR := 'novo';
    v_cenario_existente RECORD;
    v_valores_iguais BOOLEAN := FALSE;
BEGIN
    -- Verificar se já existe um cenário em produção para esta combinação
    IF p_tipo_tributo = 'icms' THEN
        SELECT * INTO v_cenario_existente FROM cenario_icms
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Comparar valores
            v_valores_iguais := (
                v_cenario_existente.origem = p_tributo_data->>'origem' AND
                v_cenario_existente.cst = p_tributo_data->>'cst' AND
                v_cenario_existente.mod_bc = p_tributo_data->>'mod_bc' AND
                v_cenario_existente.p_red_bc::TEXT = (p_tributo_data->>'p_red_bc') AND
                v_cenario_existente.aliquota::TEXT = (p_tributo_data->>'aliquota') AND
                v_cenario_existente.p_dif::TEXT = (p_tributo_data->>'p_dif')
            );

            IF v_valores_iguais THEN
                RETURN v_cenario_existente.id;
            ELSE
                v_status := 'inconsistente';
            END IF;
        END IF;

        -- Criar novo cenário
        INSERT INTO cenario_icms (
            empresa_id, escritorio_id, cliente_id, produto_id,
            origem, cst, mod_bc, p_red_bc, aliquota, p_dif,
            status, data_criacao, data_atualizacao
        ) VALUES (
            p_empresa_id, p_escritorio_id, p_cliente_id, p_produto_id,
            p_tributo_data->>'origem', p_tributo_data->>'cst', p_tributo_data->>'mod_bc',
            (p_tributo_data->>'p_red_bc')::DECIMAL, (p_tributo_data->>'aliquota')::DECIMAL,
            (p_tributo_data->>'p_dif')::DECIMAL,
            v_status, NOW(), NOW()
        ) RETURNING id INTO v_cenario_id;

    ELSIF p_tipo_tributo = 'icms_st' THEN
        SELECT * INTO v_cenario_existente FROM cenario_icms_st
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Comparar valores
            v_valores_iguais := (
                v_cenario_existente.origem = p_tributo_data->>'origem' AND
                v_cenario_existente.cst = p_tributo_data->>'cst' AND
                v_cenario_existente.mod_bc = p_tributo_data->>'mod_bc' AND
                v_cenario_existente.p_red_bc::TEXT = (p_tributo_data->>'p_red_bc') AND
                v_cenario_existente.aliquota::TEXT = (p_tributo_data->>'aliquota') AND
                v_cenario_existente.icms_st_mod_bc = p_tributo_data->>'icms_st_mod_bc' AND
                v_cenario_existente.icms_st_aliquota::TEXT = (p_tributo_data->>'icms_st_aliquota') AND
                v_cenario_existente.icms_st_p_mva::TEXT = (p_tributo_data->>'icms_st_p_mva')
            );

            IF v_valores_iguais THEN
                RETURN v_cenario_existente.id;
            ELSE
                v_status := 'inconsistente';
            END IF;
        END IF;

        -- Criar novo cenário
        INSERT INTO cenario_icms_st (
            empresa_id, escritorio_id, cliente_id, produto_id,
            origem, cst, mod_bc, p_red_bc, aliquota,
            icms_st_mod_bc, icms_st_aliquota, icms_st_p_mva,
            status, data_criacao, data_atualizacao
        ) VALUES (
            p_empresa_id, p_escritorio_id, p_cliente_id, p_produto_id,
            p_tributo_data->>'origem', p_tributo_data->>'cst', p_tributo_data->>'mod_bc',
            (p_tributo_data->>'p_red_bc')::DECIMAL, (p_tributo_data->>'aliquota')::DECIMAL,
            p_tributo_data->>'icms_st_mod_bc', (p_tributo_data->>'icms_st_aliquota')::DECIMAL,
            (p_tributo_data->>'icms_st_p_mva')::DECIMAL,
            v_status, NOW(), NOW()
        ) RETURNING id INTO v_cenario_id;

    ELSIF p_tipo_tributo = 'ipi' THEN
        SELECT * INTO v_cenario_existente FROM cenario_ipi
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Comparar valores
            v_valores_iguais := (
                v_cenario_existente.cst = p_tributo_data->>'cst' AND
                v_cenario_existente.aliquota::TEXT = (p_tributo_data->>'aliquota') AND
                v_cenario_existente.ex = p_tributo_data->>'ex'
            );

            IF v_valores_iguais THEN
                RETURN v_cenario_existente.id;
            ELSE
                v_status := 'inconsistente';
            END IF;
        END IF;

        -- Criar novo cenário
        INSERT INTO cenario_ipi (
            empresa_id, escritorio_id, cliente_id, produto_id,
            cst, aliquota, ex,
            status, data_criacao, data_atualizacao
        ) VALUES (
            p_empresa_id, p_escritorio_id, p_cliente_id, p_produto_id,
            p_tributo_data->>'cst', (p_tributo_data->>'aliquota')::DECIMAL, p_tributo_data->>'ex',
            v_status, NOW(), NOW()
        ) RETURNING id INTO v_cenario_id;

    ELSIF p_tipo_tributo = 'pis' THEN
        SELECT * INTO v_cenario_existente FROM cenario_pis
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Comparar valores
            v_valores_iguais := (
                v_cenario_existente.cst = p_tributo_data->>'cst' AND
                v_cenario_existente.aliquota::TEXT = (p_tributo_data->>'aliquota') AND
                v_cenario_existente.p_red_bc::TEXT = (p_tributo_data->>'p_red_bc')
            );

            IF v_valores_iguais THEN
                RETURN v_cenario_existente.id;
            ELSE
                v_status := 'inconsistente';
            END IF;
        END IF;

        -- Criar novo cenário
        INSERT INTO cenario_pis (
            empresa_id, escritorio_id, cliente_id, produto_id,
            cst, aliquota, p_red_bc,
            status, data_criacao, data_atualizacao
        ) VALUES (
            p_empresa_id, p_escritorio_id, p_cliente_id, p_produto_id,
            p_tributo_data->>'cst', (p_tributo_data->>'aliquota')::DECIMAL, (p_tributo_data->>'p_red_bc')::DECIMAL,
            v_status, NOW(), NOW()
        ) RETURNING id INTO v_cenario_id;

    ELSIF p_tipo_tributo = 'cofins' THEN
        SELECT * INTO v_cenario_existente FROM cenario_cofins
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Comparar valores
            v_valores_iguais := (
                v_cenario_existente.cst = p_tributo_data->>'cst' AND
                v_cenario_existente.aliquota::TEXT = (p_tributo_data->>'aliquota') AND
                v_cenario_existente.p_red_bc::TEXT = (p_tributo_data->>'p_red_bc')
            );

            IF v_valores_iguais THEN
                RETURN v_cenario_existente.id;
            ELSE
                v_status := 'inconsistente';
            END IF;
        END IF;

        -- Criar novo cenário
        INSERT INTO cenario_cofins (
            empresa_id, escritorio_id, cliente_id, produto_id,
            cst, aliquota, p_red_bc,
            status, data_criacao, data_atualizacao
        ) VALUES (
            p_empresa_id, p_escritorio_id, p_cliente_id, p_produto_id,
            p_tributo_data->>'cst', (p_tributo_data->>'aliquota')::DECIMAL, (p_tributo_data->>'p_red_bc')::DECIMAL,
            v_status, NOW(), NOW()
        ) RETURNING id INTO v_cenario_id;

    ELSIF p_tipo_tributo = 'difal' THEN
        SELECT * INTO v_cenario_existente FROM cenario_difal
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Comparar valores
            v_valores_iguais := (
                v_cenario_existente.origem = p_tributo_data->>'origem' AND
                v_cenario_existente.cst = p_tributo_data->>'cst' AND
                v_cenario_existente.mod_bc = p_tributo_data->>'mod_bc' AND
                v_cenario_existente.p_red_bc::TEXT = (p_tributo_data->>'p_red_bc') AND
                v_cenario_existente.aliquota::TEXT = (p_tributo_data->>'aliquota') AND
                v_cenario_existente.p_fcp_uf_dest::TEXT = (p_tributo_data->>'p_fcp_uf_dest') AND
                v_cenario_existente.p_icms_uf_dest::TEXT = (p_tributo_data->>'p_icms_uf_dest') AND
                v_cenario_existente.p_icms_inter::TEXT = (p_tributo_data->>'p_icms_inter') AND
                v_cenario_existente.p_icms_inter_part::TEXT = (p_tributo_data->>'p_icms_inter_part')
            );

            IF v_valores_iguais THEN
                RETURN v_cenario_existente.id;
            ELSE
                v_status := 'inconsistente';
            END IF;
        END IF;

        -- Criar novo cenário
        INSERT INTO cenario_difal (
            empresa_id, escritorio_id, cliente_id, produto_id,
            origem, cst, mod_bc, p_red_bc, aliquota,
            p_fcp_uf_dest, p_icms_uf_dest, p_icms_inter, p_icms_inter_part,
            status, data_criacao, data_atualizacao
        ) VALUES (
            p_empresa_id, p_escritorio_id, p_cliente_id, p_produto_id,
            p_tributo_data->>'origem', p_tributo_data->>'cst', p_tributo_data->>'mod_bc',
            (p_tributo_data->>'p_red_bc')::DECIMAL, (p_tributo_data->>'aliquota')::DECIMAL,
            (p_tributo_data->>'p_fcp_uf_dest')::DECIMAL, (p_tributo_data->>'p_icms_uf_dest')::DECIMAL,
            (p_tributo_data->>'p_icms_inter')::DECIMAL, (p_tributo_data->>'p_icms_inter_part')::DECIMAL,
            v_status, NOW(), NOW()
        ) RETURNING id INTO v_cenario_id;
    END IF;

    RETURN v_cenario_id;
END;
$$ LANGUAGE plpgsql;

-- Função para verificar cenários compatíveis com produção
-- Em vez de atualizar para 'conforme', agora apenas retorna o número de cenários compatíveis
CREATE OR REPLACE FUNCTION verificar_cenarios_compativeis(
    p_empresa_id INTEGER,
    p_cliente_id INTEGER,
    p_produto_id INTEGER,
    p_tipo_tributo VARCHAR
) RETURNS INTEGER AS $$
DECLARE
    v_cenario_producao RECORD;
    v_count INTEGER := 0;
BEGIN
    -- Buscar o cenário em produção
    IF p_tipo_tributo = 'icms' THEN
        SELECT * INTO v_cenario_producao FROM cenario_icms
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Contar cenários 'novo' e 'inconsistente' que são compatíveis
            SELECT COUNT(*) INTO v_count FROM cenario_icms
            WHERE empresa_id = p_empresa_id
            AND cliente_id = p_cliente_id
            AND produto_id = p_produto_id
            AND status IN ('novo', 'inconsistente')
            AND origem = v_cenario_producao.origem
            AND cst = v_cenario_producao.cst
            AND mod_bc = v_cenario_producao.mod_bc
            AND p_red_bc = v_cenario_producao.p_red_bc
            AND aliquota = v_cenario_producao.aliquota
            AND p_dif = v_cenario_producao.p_dif;
        END IF;

    ELSIF p_tipo_tributo = 'icms_st' THEN
        SELECT * INTO v_cenario_producao FROM cenario_icms_st
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Contar cenários 'novo' e 'inconsistente' que são compatíveis
            SELECT COUNT(*) INTO v_count FROM cenario_icms_st
            WHERE empresa_id = p_empresa_id
            AND cliente_id = p_cliente_id
            AND produto_id = p_produto_id
            AND status IN ('novo', 'inconsistente')
            AND origem = v_cenario_producao.origem
            AND cst = v_cenario_producao.cst
            AND mod_bc = v_cenario_producao.mod_bc
            AND p_red_bc = v_cenario_producao.p_red_bc
            AND aliquota = v_cenario_producao.aliquota
            AND icms_st_mod_bc = v_cenario_producao.icms_st_mod_bc
            AND icms_st_aliquota = v_cenario_producao.icms_st_aliquota
            AND icms_st_p_mva = v_cenario_producao.icms_st_p_mva;
        END IF;

    ELSIF p_tipo_tributo = 'ipi' THEN
        SELECT * INTO v_cenario_producao FROM cenario_ipi
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Contar cenários 'novo' e 'inconsistente' que são compatíveis
            SELECT COUNT(*) INTO v_count FROM cenario_ipi
            WHERE empresa_id = p_empresa_id
            AND cliente_id = p_cliente_id
            AND produto_id = p_produto_id
            AND status IN ('novo', 'inconsistente')
            AND cst = v_cenario_producao.cst
            AND aliquota = v_cenario_producao.aliquota
            AND ex = v_cenario_producao.ex;
        END IF;

    ELSIF p_tipo_tributo = 'pis' THEN
        SELECT * INTO v_cenario_producao FROM cenario_pis
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Contar cenários 'novo' e 'inconsistente' que são compatíveis
            SELECT COUNT(*) INTO v_count FROM cenario_pis
            WHERE empresa_id = p_empresa_id
            AND cliente_id = p_cliente_id
            AND produto_id = p_produto_id
            AND status IN ('novo', 'inconsistente')
            AND cst = v_cenario_producao.cst
            AND aliquota = v_cenario_producao.aliquota
            AND p_red_bc = v_cenario_producao.p_red_bc;
        END IF;

    ELSIF p_tipo_tributo = 'cofins' THEN
        SELECT * INTO v_cenario_producao FROM cenario_cofins
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Contar cenários 'novo' e 'inconsistente' que são compatíveis
            SELECT COUNT(*) INTO v_count FROM cenario_cofins
            WHERE empresa_id = p_empresa_id
            AND cliente_id = p_cliente_id
            AND produto_id = p_produto_id
            AND status IN ('novo', 'inconsistente')
            AND cst = v_cenario_producao.cst
            AND aliquota = v_cenario_producao.aliquota
            AND p_red_bc = v_cenario_producao.p_red_bc;
        END IF;

    ELSIF p_tipo_tributo = 'difal' THEN
        SELECT * INTO v_cenario_producao FROM cenario_difal
        WHERE empresa_id = p_empresa_id
        AND cliente_id = p_cliente_id
        AND produto_id = p_produto_id
        AND status = 'producao'
        AND ativo = TRUE;

        IF FOUND THEN
            -- Contar cenários 'novo' e 'inconsistente' que são compatíveis
            SELECT COUNT(*) INTO v_count FROM cenario_difal
            WHERE empresa_id = p_empresa_id
            AND cliente_id = p_cliente_id
            AND produto_id = p_produto_id
            AND status IN ('novo', 'inconsistente')
            AND origem = v_cenario_producao.origem
            AND cst = v_cenario_producao.cst
            AND mod_bc = v_cenario_producao.mod_bc
            AND p_red_bc = v_cenario_producao.p_red_bc
            AND aliquota = v_cenario_producao.aliquota
            AND p_fcp_uf_dest = v_cenario_producao.p_fcp_uf_dest
            AND p_icms_uf_dest = v_cenario_producao.p_icms_uf_dest
            AND p_icms_inter = v_cenario_producao.p_icms_inter
            AND p_icms_inter_part = v_cenario_producao.p_icms_inter_part;
        END IF;
    END IF;

    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Comentários sobre o script
COMMENT ON TABLE cenario_icms IS 'Armazena cenários de ICMS para combinações de empresa/cliente/produto';
COMMENT ON TABLE cenario_icms_st IS 'Armazena cenários de ICMS-ST para combinações de empresa/cliente/produto';
COMMENT ON TABLE cenario_ipi IS 'Armazena cenários de IPI para combinações de empresa/cliente/produto';
COMMENT ON TABLE cenario_pis IS 'Armazena cenários de PIS para combinações de empresa/cliente/produto';
COMMENT ON TABLE cenario_cofins IS 'Armazena cenários de COFINS para combinações de empresa/cliente/produto';
COMMENT ON TABLE cenario_difal IS 'Armazena cenários de DIFAL para combinações de empresa/cliente/produto';

COMMENT ON FUNCTION calcular_tributos_cenario(INTEGER) IS 'Calcula os valores de tributos com base nos cenários ativos em produção';
COMMENT ON FUNCTION atualizar_status_tributos_produto(INTEGER, VARCHAR, VARCHAR) IS 'Atualiza o status de todos os tributos relacionados a um produto';
COMMENT ON FUNCTION criar_cenario_importacao(INTEGER, INTEGER, INTEGER, INTEGER, VARCHAR, JSONB) IS 'Cria cenários durante a importação XML';
COMMENT ON FUNCTION verificar_cenarios_compativeis(INTEGER, INTEGER, INTEGER, VARCHAR) IS 'Verifica e conta cenários compatíveis com o cenário em produção';
