Detailed Recommendations

1. Database Schema Changes
   I've created a migration SQL file that includes:

The nota_fiscal_item table to store invoice-specific data
Added NCM columns to all scenario tables
Added references to scenarios in the tributo table
Created new tables for audit results and summaries 2. Handling CFOP and NCM Differences
For CFOP:
When importing an XML, store the CFOP in both the nota_fiscal_item table and the scenario tables
When creating scenarios, include the CFOP as part of the uniqueness check
This means that the same product with different CFOPs will create different scenarios with status 'novo'
For NCM:
Store the NCM in both the nota_fiscal_item table and the scenario tables
When creating scenarios, check if the NCM is different from existing scenarios
If there's a production scenario with a different NCM, create a new scenario with status 'inconsistente' 3. Audit Process Improvements
Store detailed audit results in the new auditoria_resultado table
Store summary information in the auditoria_sumario table
Link each audit result to the specific scenario used for the calculation
Display detailed comparisons between invoice values and calculated values 4. Implementation Strategy
Here's how I recommend implementing these changes:

Apply the Database Migration:
Run the SQL migration script to create the new tables and columns
Update the XML Import Process:
Modify the import process to create nota_fiscal_item records
Update the scenario creation logic to handle CFOP and NCM differences
Update the Audit Process:
Modify the audit process to store results in the new tables
Update the audit calculation to use the appropriate scenarios
Update the UI:
Create new views to display the audit results and summaries
Show detailed comparisons between invoice and calculated values
Code Changes Required
Here are the key code changes that would be needed:

XML Import Service:
Create nota_fiscal_item records during import
Link tributo records to nota_fiscal_item records
Update scenario creation to handle CFOP and NCM differences
Cenario Service:
Update the scenario creation logic to check for CFOP and NCM differences
Create new scenarios when CFOP is different
Create inconsistent scenarios when NCM is different
Auditoria Service:
Store audit results in the new tables
Link audit results to specific scenarios
Calculate summary information
UI Components:
Create new views for the audit page
Display detailed comparisons
Show summary information in cards
Conclusion
The approach I've outlined addresses your requirements by:

Storing invoice-specific data in the nota_fiscal_item table
Creating different scenarios based on CFOP differences
Flagging NCM differences by creating inconsistent scenarios
Improving the audit process with detailed results and summaries
Enhancing the UI to show detailed comparisons and summaries
This solution maintains the integrity of your product data while allowing for different tax scenarios based on CFOP and NCM variations. It also provides a robust framework for auditing and displaying the results
