"""
Serviço de importação em lote com controle de concorrência melhorado
"""
from typing import List, Dict
from models import Empresa
from .xml_import_service import XMLImportService
from .transaction_manager import get_transaction_manager
from utils import XMLProcessor
import traceback
import threading
import time
import logging

logger = logging.getLogger(__name__)

class BatchXMLImportService:
    """
    Serviço para importação em lote de arquivos XML
    """

    def __init__(self, escritorio_id: int, usuario_id: int, max_workers: int = 4, max_files_per_batch: int = 50, progress_callback=None):
        """
        Inicializa o serviço de importação em lote

        Args:
            escritorio_id (int): ID do escritório
            usuario_id (int): ID do usuário que está realizando a importação
            max_workers (int): Número máximo de threads para processamento paralelo
            max_files_per_batch (int): Número máximo de arquivos por lote para evitar sobrecarga
            progress_callback (callable): Função callback para reportar progresso
        """
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
        self.max_workers = max_workers
        self.max_files_per_batch = max_files_per_batch
        self.progress_callback = progress_callback

    def process_xml_batch(self, xml_files: List[Dict]) -> Dict:
        """
        Processa um lote de arquivos XML com controle de concorrência

        Args:
            xml_files (List[Dict]): Lista de dicionários contendo {filename, content} dos XMLs

        Returns:
            Dict: Resultado do processamento em lote
        """
        logger.info(f"Iniciando processamento de {len(xml_files)} arquivos")

        # Resultados do processamento
        results = {
            'success': [],
            'errors': [],
            'total': len(xml_files),
            'processed': 0
        }

        # Obter gerenciador de transações
        transaction_manager = get_transaction_manager()

        try:
            # Agrupar XMLs por empresa para otimizar o processamento
            logger.info("Agrupando XMLs por empresa...")
            xml_by_company = self._group_by_company(xml_files)
            logger.info(f"XMLs agrupados em {len(xml_by_company)} empresas")

            # Processar cada empresa sequencialmente para evitar problemas de contexto
            print(f"[BATCH SERVICE] Iniciando processamento sequencial")
            for empresa_id, empresa_xmls in xml_by_company.items():
                print(f"[BATCH SERVICE] Processando {len(empresa_xmls)} XMLs da empresa {empresa_id}")

                try:
                    batch_result = self._process_company_batch(empresa_id, empresa_xmls)
                    results['success'].extend(batch_result['success'])
                    results['errors'].extend(batch_result['errors'])
                    results['processed'] += len(batch_result['success']) + len(batch_result['errors'])
                    print(f"[BATCH SERVICE] Processado lote com {len(batch_result['success'])} sucessos e {len(batch_result['errors'])} erros")
                except Exception as e:
                    error_trace = traceback.format_exc()
                    print(f"[BATCH SERVICE] Erro ao processar empresa {empresa_id}: {str(e)}")
                    print(f"[BATCH SERVICE] Stack trace:\n{error_trace}")
                    results['errors'].append({
                        'message': f'Erro no processamento da empresa {empresa_id}: {str(e)}',
                        'stack_trace': error_trace
                    })

        except Exception as e:
            error_trace = traceback.format_exc()
            print(f"[BATCH SERVICE] Erro geral no processamento em lote: {str(e)}")
            print(f"[BATCH SERVICE] Stack trace:\n{error_trace}")
            results['errors'].append({
                'message': f'Erro geral no processamento em lote: {str(e)}',
                'stack_trace': error_trace
            })

        finally:
            print(f"[BATCH SERVICE] Processamento finalizado: {results['processed']} XMLs processados")
            print(f"[BATCH SERVICE] Sucessos: {len(results['success'])}, Erros: {len(results['errors'])}")
            return results

    def _group_by_company(self, xml_files: List[Dict]) -> Dict[int, List[Dict]]:
        """
        Agrupa os XMLs por empresa baseado no CNPJ do emitente

        Args:
            xml_files (List[Dict]): Lista de arquivos XML

        Returns:
            Dict[int, List[Dict]]: XMLs agrupados por ID da empresa
        """
        xml_by_company = {}

        for xml_file in xml_files:
            try:
                # Extrair CNPJ do emitente
                processor = XMLProcessor(xml_file['content'])
                emitente = processor.get_emitente()
                cnpj = emitente.get('cnpj')

                if not cnpj:
                    continue

                # Buscar empresa (já estamos dentro do contexto da aplicação)
                empresa = Empresa.query.filter_by(cnpj=cnpj).first()
                if not empresa:
                    continue

                # Agrupar por empresa
                if empresa.id not in xml_by_company:
                    xml_by_company[empresa.id] = []

                xml_by_company[empresa.id].append(xml_file)

            except Exception as e:
                print(f"Erro ao processar arquivo {xml_file['filename']}: {str(e)}")
                continue

        return xml_by_company

    def _process_company_batch(self, empresa_id: int, xml_files: List[Dict]) -> Dict:
        """
        Processa um lote de XMLs de uma empresa específica com controle de transações

        Args:
            empresa_id (int): ID da empresa
            xml_files (List[Dict]): Lista de arquivos XML da empresa

        Returns:
            Dict: Resultado do processamento
        """
        results = {
            'success': [],
            'errors': []
        }

        # Obter gerenciador de transações
        transaction_manager = get_transaction_manager()

        # Usar transação controlada para toda a empresa
        with transaction_manager.bulk_operation(
            empresa_id=empresa_id,
            table_name='tributo',
            batch_size=self.max_files_per_batch
        ) as batch_processor:

            # Buscar empresa para obter o escritório_id correto
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                raise ValueError(f'Empresa com ID {empresa_id} não encontrada')

            # Criar serviço de importação para a empresa
            service = XMLImportService(
                empresa_id=empresa_id,
                escritorio_id=empresa.escritorio_id,  # Usar o escritório da empresa
                usuario_id=self.usuario_id
            )

            # Dividir os arquivos em lotes menores para evitar sobrecarga
            total_files = len(xml_files)
            logger.info(f"Processando {total_files} arquivos para empresa {empresa_id}")

            # Dividir em lotes menores
            for i in range(0, total_files, self.max_files_per_batch):
                batch = xml_files[i:i + self.max_files_per_batch]
                logger.info(f"Processando lote {i//self.max_files_per_batch + 1} com {len(batch)} arquivos")

                # Processar cada XML no lote
                for xml_file in batch:
                    try:
                        # Adicionar tratamento de memória e timeout
                        import gc
                        import time

                        # Forçar coleta de lixo antes de processar cada arquivo
                        gc.collect()

                        # Registrar tempo de início para monitoramento
                        start_time = time.time()

                        result = service.import_xml(
                            xml_content=xml_file['content'],
                            filename=xml_file['filename']
                        )

                        # Registrar tempo de processamento
                        processing_time = time.time() - start_time
                        logger.info(f"Arquivo {xml_file['filename']} processado em {processing_time:.2f} segundos")

                        if result['success']:
                            results['success'].append({
                                'filename': xml_file['filename'],
                                'importacao_id': result.get('importacao_id'),
                                'message': 'Importado com sucesso',
                                'processing_time': processing_time
                            })
                        else:
                            results['errors'].append({
                                'filename': xml_file['filename'],
                                'message': result.get('message'),
                                'error': result.get('error')
                            })

                        # Reportar progresso se callback estiver disponível
                        if self.progress_callback:
                            processed_count = len(results['success']) + len(results['errors'])
                            progress_data = {
                                'processed': processed_count,
                                'total': total_files,
                                'current_file': xml_file['filename'],
                                'success_count': len(results['success']),
                                'error_count': len(results['errors']),
                                'empresa_id': empresa_id
                            }
                            logger.debug(f"Enviando progresso: {progress_data}")
                            self.progress_callback(progress_data)

                    except Exception as e:
                        error_trace = traceback.format_exc()
                        logger.error(f"Erro ao processar XML {xml_file['filename']}: {str(e)}")
                        logger.error(f"Stack trace:\n{error_trace}")

                        results['errors'].append({
                            'filename': xml_file['filename'],
                            'message': f'Erro ao processar XML: {str(e)}',
                            'stack_trace': error_trace
                        })

                        # Reportar progresso mesmo em caso de erro
                        if self.progress_callback:
                            processed_count = len(results['success']) + len(results['errors'])
                            progress_data = {
                                'processed': processed_count,
                                'total': total_files,
                                'current_file': xml_file['filename'],
                                'success_count': len(results['success']),
                                'error_count': len(results['errors']),
                                'empresa_id': empresa_id,
                                'error': str(e)
                            }
                            logger.debug(f"Enviando progresso (erro): {progress_data}")
                            self.progress_callback(progress_data)

                        # Forçar coleta de lixo após erro
                        gc.collect()

                # Pausa entre lotes para permitir que o sistema se recupere
                if i + self.max_files_per_batch < total_files:
                    logger.debug("Pausa entre lotes...")
                    time.sleep(0.5)  # Pausa reduzida entre lotes

            # Finalizar processamento em lote
            batch_processor.finish()

        return results
