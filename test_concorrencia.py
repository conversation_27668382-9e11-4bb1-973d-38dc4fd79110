#!/usr/bin/env python3
"""
Script de teste para verificar se as melhorias de concorrência estão funcionando
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'back'))

from app import create_app
from services.queue_manager import get_queue_manager
from services.transaction_manager import get_transaction_manager
import time

def test_queue_manager():
    """Testa o gerenciador de filas"""
    print("🔄 Testando Queue Manager...")
    
    try:
        queue_manager = get_queue_manager()
        stats = queue_manager.get_queue_stats()
        
        print(f"✅ Queue Manager funcionando:")
        print(f"   - Fila de importação: {stats['import_queue_size']} tarefas")
        print(f"   - Fila de auditoria: {stats['audit_queue_size']} tarefas")
        print(f"   - Tarefas ativas: {stats['active_tasks']}")
        print(f"   - Total processado: {stats['stats']['total_tasks']}")
        
        return True
    except Exception as e:
        print(f"❌ Erro no Queue Manager: {str(e)}")
        return False

def test_transaction_manager():
    """Testa o gerenciador de transações"""
    print("\n🔒 Testando Transaction Manager...")
    
    try:
        transaction_manager = get_transaction_manager()
        stats = transaction_manager.get_stats()
        
        print(f"✅ Transaction Manager funcionando:")
        print(f"   - Transações totais: {stats['total_transactions']}")
        print(f"   - Transações bem-sucedidas: {stats['successful_transactions']}")
        print(f"   - Transações falhadas: {stats['failed_transactions']}")
        print(f"   - Taxa de sucesso: {stats['success_rate']:.1f}%")
        print(f"   - Deadlocks detectados: {stats['deadlocks_detected']}")
        
        return True
    except Exception as e:
        print(f"❌ Erro no Transaction Manager: {str(e)}")
        return False

def test_database_pool():
    """Testa o pool de conexões do banco"""
    print("\n💾 Testando Pool de Conexões...")
    
    try:
        app, socketio = create_app()
        
        with app.app_context():
            from models import db
            
            # Testar conexão básica
            result = db.session.execute(db.text('SELECT 1 as test')).fetchone()
            
            if result and result[0] == 1:
                print("✅ Conexão com banco funcionando")
                
                # Verificar configurações do pool
                engine = db.engine
                pool = engine.pool
                
                print(f"   - Pool size: {pool.size()}")
                print(f"   - Checked in: {pool.checkedin()}")
                print(f"   - Checked out: {pool.checkedout()}")
                print(f"   - Overflow: {pool.overflow()}")
                print(f"   - Invalid: {pool.invalid()}")
                
                return True
            else:
                print("❌ Falha na consulta de teste")
                return False
                
    except Exception as e:
        print(f"❌ Erro no pool de conexões: {str(e)}")
        return False

def test_websocket_service():
    """Testa o serviço WebSocket"""
    print("\n🌐 Testando WebSocket Service...")
    
    try:
        from services.websocket_service import get_websocket_service
        
        websocket_service = get_websocket_service()
        
        if websocket_service:
            print("✅ WebSocket Service funcionando")
            return True
        else:
            print("❌ WebSocket Service não inicializado")
            return False
            
    except Exception as e:
        print(f"❌ Erro no WebSocket Service: {str(e)}")
        return False

def test_system_health():
    """Testa a saúde geral do sistema"""
    print("\n🏥 Testando Saúde do Sistema...")
    
    try:
        app, socketio = create_app()
        
        with app.test_client() as client:
            # Testar health check
            response = client.get('/api/system/health')
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"✅ Health check: {data['status']}")
                print(f"   - Database: {data['database']}")
                return True
            else:
                print(f"❌ Health check falhou: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Erro no health check: {str(e)}")
        return False

def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes de concorrência...\n")
    
    tests = [
        test_database_pool,
        test_queue_manager,
        test_transaction_manager,
        test_websocket_service,
        test_system_health
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.5)  # Pausa entre testes
    
    print(f"\n📊 Resultado dos testes:")
    print(f"   ✅ Passou: {passed}/{total}")
    print(f"   ❌ Falhou: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 Todos os testes passaram! Sistema pronto para uso simultâneo.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} teste(s) falharam. Verifique os logs acima.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
