Mudanças cruciais no sistema:
Reformulação total. Atualmente temos uma coluna de status para cada tipo de tributo e um status geral do tributo, podemos remover e inutilizar. Mudaremos tudo, criaremos novas tabelas de cenário para cada tipo de tributo: cenario_icms, cenario_icms_st, cenario_pis, etc.
O Cenário será composto por dados do escritório, empresa, cliente, produto e tributo. Se fizermos esse relacionamento, por ID, o processo será mais lento? Pois creio que seria o melhor, pois se a informação do cliente for atualizada, o cenário pega as informações do cliente da sua própria tabela, então já mostraria o dado atualizado

Teremos 3 status dentro de cada cenário: novo, producao e inconsistente.
Teremos que ter algo para editar o tempo de vigência do cenário, e algo para ativarmos ou desativarmos os cenários com o status producao.
É melhor criarmos os cenários logo na importação? Ou você acha que será demora e lento? Nunca devemos criar cenários duplicados (mesma empresa, mesmo cliente, mesmo produto e mesma taxa tributaria), criaremos um novo apenas se algo disso mudar.

Como serão criados os cenários?
Cenários "novo": todos os cenários que não temos no sistema
Cenário "producao": cenários que tiveram seu status manualmente atualizado pelo usuário.
Cenário "inconsistente": cenários que tiveram seus status manualmente atualizados, ou cenários criados durante a importação, que não "bateram" com os tributos do atual cenário em produção.

O que veremos na lista dos detalhes do cenário?
NOVOS: Botões para atualizar os status. Botão para editar (mesmo modal atual, com informações gerais, cliente, produto e tributo (podemos excluir a tab de 'histórico e todas suas funcionalidades)).
PRODUÇÃO: Botão para atualizar sua vigência, botão para ativar ou desativar o cenário. Mesmo botão para editar
INCONSISTENTE: Botão para excluir, botão para atualizar seu status para produção, que ao clicar abre um modal para adicionar a data de vigência do cenário que estava em produção. Mesmo botão para editar.

Atualizar a página de Cenário Detalhes para mostrar os dados das novas tabelas (cenarios_{tributo}).
NAO TEREMOS CENARIOS DUPLICADOS.

Temos que reformular todo o sistema atual de status, não precisamos dos status na tabela tributos.
O status da tabela produtos, pode ser utilizado para executarmos ou não, o calculo de sua tributação com base nos dados de cenário em produção. Se o cálculo com base no dados do Cenário em produção já foi feito, para os tributos daquele produto, marcamos o produto com status 'conforme', caso contrário, o status é 'nulo'.

O status da tabela cliente, agora informará se as informações de 'Atividade' e 'Destinação', se ter os dois campos preenchidos, o status é 'ok', se faltar alguma das duas informações ou faltar as duas, informaremos com 3 status diferentes, para que seja possível filtrar pelos status na página de Clientes.


Produtos / Tributos
Na tabela de tributos, temos que ter as novas colunas para os mesmos, que serão as informações de tributo do cenário que esta em produção (levando em consideração sua data de vigência e se esta ativo ou não). Então, pegaremos todas as informações da nota, e depois pegaremos os dados do cenário (%ICMS) por exemplo, e faremos o calculo com esse valor.
Teremos que "duplicar" as colunas de tributos para fazermos o cálculo com as informações corretas, do cenário que está em produção.

Como faremos para atualizar as novas colunas, caso não tenhamos seu cenário em produção? 

Ajuda:
Se em uma importação o tributo de ICMS ser diferente do cenário de ICMS em produção da mesma empresa, cliente, produto mas o restante dos tributos "baterem", criaremos apenas um novo cenário de ICMS, com o status "inconsistente", concorda?

A tabela de produto e tributos continuara existindo e será crucial para elaborarmos posteriormente a página de Auditoria.


Dados para alimentar os Cenários:
Temos uma nova tag para pegar na importação do XML: <EXTIPI>, esta tag ficará dentro do grupo de informações de produto <prod>, essa TAG deve alimentar o tributo de ipi, que pode ser chamada de ipi_ex (devemos criar uma nova coluna no tributo)
No banco de dados, temos que criar uma nova coluna para os tributos de pis /cofins, que é o dado de Percentual de Red. BC. Esse dado não virá da importação, mas devemos telo, pois o usuário conseguirá alteralo (adicionalo) dentro do cenário.

Quais TAGs de tributos iremos usar para alimentar os Cenários de cada tipo de tributo?
ICMS: icms_origem, icms_cst, icms_mod_bc, icms_p_red_bc, icms_aliquota e icms_p_dif
ICMS ST: icms_origem, icms_cst, icms_mod_bc, icms_p_red_bc, icms_aliquota, icms_st_mod_bc, icms_st_aliquota e icms_st_p_mva
IPI: ipi_cst, ipi_aliquota e o novo ipi_ex
PIS: pis_cst, pis_aliquota e novo dado de percentual red bc pis
COFINS: cofins_cst, cofins_aliquota e novo dado de percentual red bc pis
DIFAL: icms_origem, icms_cst, icms_mod_bc, icms_p_red_bc, icms_aliquota, difal_p_fcp_uf_dest, difal_p_icms_uf_dest, difal_p_icms_inter e difal_p_icms_inter_part

Além de claro, o relacionamento com o escritório, empresa, cliente e produto.